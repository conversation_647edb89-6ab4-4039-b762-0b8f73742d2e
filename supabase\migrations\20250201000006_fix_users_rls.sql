-- Fix users table RLS recursion issue
-- The problem is still in the users table policies

-- =============================================
-- DROP USERS POLICIES AND RECREATE SIMPLY
-- =============================================

-- Drop all users policies
DROP POLICY IF EXISTS "Anyone can view user profiles" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Users can create own profile" ON public.users;
DROP POLICY IF EXISTS "Service role can manage all users" ON public.users;

-- =============================================
-- SIMPLE USERS TABLE POLICIES (NO RECURSION)
-- =============================================

-- Anyone can view basic user info (needed for public profiles, reviews, etc.)
CREATE POLICY "Public user info viewable" ON public.users
    FOR SELECT USING (true);

-- Users can update their own profile only
CREATE POLICY "Users update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Users can create their own profile during registration
CREATE POLICY "Users create own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Allow service role full access (for admin operations)
CREATE POLICY "Service role full access" ON public.users
    FOR ALL USING (auth.role() = 'service_role');
