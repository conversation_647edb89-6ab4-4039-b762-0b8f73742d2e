-- Create cities table and populate with Moroccan cities
-- This table will be used for location filtering and agency locations

-- =============================================
-- CITIES TABLE
-- =============================================
CREATE TABLE public.cities (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    name_ar TEXT, -- Arabic name
    name_fr TEXT, -- French name
    region TEXT,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    is_popular BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_cities_name ON public.cities(name);
CREATE INDEX idx_cities_region ON public.cities(region);
CREATE INDEX idx_cities_popular ON public.cities(is_popular);

-- Enable RLS
ALTER TABLE public.cities ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Anyone can view cities" ON public.cities
    FOR SELECT USING (true);

CREATE POLICY "Admins can manage cities" ON public.cities
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- POPULATE CITIES TABLE
-- =============================================

INSERT INTO public.cities (name, name_ar, name_fr, region, latitude, longitude, is_popular) VALUES
-- Major cities (popular destinations)
('Casablanca', 'الدار البيضاء', 'Casablanca', 'Casablanca-Settat', 33.5731, -7.5898, true),
('Marrakech', 'مراكش', 'Marrakech', 'Marrakech-Safi', 31.6295, -7.9811, true),
('Rabat', 'الرباط', 'Rabat', 'Rabat-Salé-Kénitra', 34.0209, -6.8416, true),
('Fes', 'فاس', 'Fès', 'Fès-Meknès', 34.0181, -5.0078, true),
('Tangier', 'طنجة', 'Tanger', 'Tanger-Tétouan-Al Hoceïma', 35.7595, -5.8340, true),
('Agadir', 'أكادير', 'Agadir', 'Souss-Massa', 30.4278, -9.5981, true),
('Meknes', 'مكناس', 'Meknès', 'Fès-Meknès', 33.8935, -5.5473, true),
('Oujda', 'وجدة', 'Oujda', 'Oriental', 34.6814, -1.9086, true),

-- Other important cities
('Kenitra', 'القنيطرة', 'Kénitra', 'Rabat-Salé-Kénitra', 34.2610, -6.5802, false),
('Tetouan', 'تطوان', 'Tétouan', 'Tanger-Tétouan-Al Hoceïma', 35.5889, -5.3626, false),
('Safi', 'آسفي', 'Safi', 'Marrakech-Safi', 32.2994, -9.2372, false),
('El Jadida', 'الجديدة', 'El Jadida', 'Casablanca-Settat', 33.2316, -8.5007, false),
('Beni Mellal', 'بني ملال', 'Béni Mellal', 'Béni Mellal-Khénifra', 32.3373, -6.3498, false),
('Nador', 'الناظور', 'Nador', 'Oriental', 35.1681, -2.9287, false),
('Taza', 'تازة', 'Taza', 'Fès-Meknès', 34.2133, -4.0103, false),
('Khouribga', 'خريبكة', 'Khouribga', 'Béni Mellal-Khénifra', 32.8811, -6.9063, false),
('Settat', 'سطات', 'Settat', 'Casablanca-Settat', 33.0013, -7.6216, false),
('Mohammedia', 'المحمدية', 'Mohammedia', 'Casablanca-Settat', 33.6866, -7.3830, false),
('Khemisset', 'الخميسات', 'Khémisset', 'Rabat-Salé-Kénitra', 33.8244, -6.0661, false),
('Larache', 'العرائش', 'Larache', 'Tanger-Tétouan-Al Hoceïma', 35.1932, -6.1557, false),
('Guelmim', 'كلميم', 'Guelmim', 'Guelmim-Oued Noun', 29.0217, -10.0574, false),
('Berkane', 'بركان', 'Berkane', 'Oriental', 34.9218, -2.3200, false),
('Ouarzazate', 'ورزازات', 'Ouarzazate', 'Drâa-Tafilalet', 30.9335, -6.9370, false),
('Errachidia', 'الراشيدية', 'Errachidia', 'Drâa-Tafilalet', 31.9314, -4.4244, false),
('Essaouira', 'الصويرة', 'Essaouira', 'Marrakech-Safi', 31.5085, -9.7595, false),
('Al Hoceima', 'الحسيمة', 'Al Hoceïma', 'Tanger-Tétouan-Al Hoceïma', 35.2517, -3.9372, false),
('Tiznit', 'تزنيت', 'Tiznit', 'Souss-Massa', 29.6974, -9.7316, false),
('Taourirt', 'تاوريرت', 'Taourirt', 'Oriental', 34.4092, -2.8953, false),
('Sidi Kacem', 'سيدي قاسم', 'Sidi Kacem', 'Rabat-Salé-Kénitra', 34.2214, -5.7081, false),
('Sidi Slimane', 'سيدي سليمان', 'Sidi Slimane', 'Rabat-Salé-Kénitra', 34.2654, -5.9228, false),
('Azrou', 'أزرو', 'Azrou', 'Fès-Meknès', 33.4346, -5.2217, false),
('Midelt', 'ميدلت', 'Midelt', 'Drâa-Tafilalet', 32.6852, -4.7330, false),
('Ifrane', 'إفران', 'Ifrane', 'Fès-Meknès', 33.5228, -5.1106, false),
('Chefchaouen', 'شفشاون', 'Chefchaouen', 'Tanger-Tétouan-Al Hoceïma', 35.1688, -5.2636, false),
('Dakhla', 'الداخلة', 'Dakhla', 'Dakhla-Oued Ed-Dahab', 23.7185, -15.9570, false),
('Laayoune', 'العيون', 'Laâyoune', 'Laâyoune-Sakia El Hamra', 27.1253, -13.1625, false);

-- Update trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_cities_updated_at 
    BEFORE UPDATE ON public.cities 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
