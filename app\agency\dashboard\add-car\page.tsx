"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Upload, X, Shield, DollarSign } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "sonner"
import { AuthGuard } from "@/components/features/auth/auth-guard"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import Image from 'next/image'
import { FileUpload } from '@/components/ui/file-upload'
import { supabase } from '@/lib/supabase/client'
import { useAuth } from '@/contexts/auth-context'

import { CAR_BRANDS, CAR_COLORS, CAR_CATEGORIES } from '@/utils/constants';

const availableFeatures = [
  { key: 'ac', name: 'AC', icon: '/icons/car-air-conditioning-svgrepo-com.svg', type: 'boolean' },
  { key: 'bluetooth', name: 'Bluetooth', icon: '/icons/bluetooth-on-svgrepo-com.svg', type: 'boolean' },
  { key: 'airbags', name: 'Airbags', icon: '/icons/airbags.svg', type: 'boolean' },
  { key: 'gps', name: 'GPS', icon: '/icons/gps.svg', type: 'boolean' },
  { key: 'usbCharger', name: 'USB Charger', icon: '/icons/usb.svg', type: 'boolean' },
  { key: 'sunroof', name: 'Sunroof', icon: '/icons/sunroof.svg', type: 'boolean' },
];

const bodyTypeOptions = ['SUV', 'Sedan', 'Coupe', 'Convertible', 'Hatchback', 'Wagon', 'Van', 'Truck'];
const fuelTypeOptions = ['gasoline', 'diesel', 'hybrid', 'electric', 'plug-in_hybrid', 'hydrogen'];

const moroccanCities = [
  "Marrakech", "Casablanca", "Rabat", "Fes", "Tangier", "Agadir", "Oujda", "Kenitra", "Tetouan", "Safi",
  "El Jadida", "Beni Mellal", "Nador", "Taza", "Khouribga", "Settat", "Mohammedia", "Khemisset", "Larache", "Guelmim",
  "Berkane", "Ouarzazate", "Errachidia", "Essaouira", "Al Hoceima", "Tiznit", "Taourirt", "Sidi Kacem", "Sidi Slimane", "Azrou", "Midelt"
];

function ColorPicker({ value, onChange }: { value: string; onChange: (color: string) => void }) {
  return (
    <div className="flex gap-2 flex-wrap">
      {CAR_COLORS.map((color) => (
        <button
          key={color}
          type="button"
          className={`w-8 h-8 rounded border-2 flex items-center justify-center focus:outline-none ${value === color ? 'border-blue-600 ring-2 ring-blue-300' : 'border-gray-300'}`}
          style={{ backgroundColor: color.toLowerCase() }}
          aria-label={color}
          onClick={() => onChange(color)}
        >
          {value === color && <span className="w-3 h-3 rounded-full bg-white border border-blue-600" />}
        </button>
      ))}
    </div>
  );
}

export default function AddCarPage() {
  const router = useRouter()
  const { user } = useAuth()
  const [carImages, setCarImages] = useState<string[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [insuranceType, setInsuranceType] = useState("basic")
  const [agencyProfile, setAgencyProfile] = useState<any>(null)

  // Load agency profile on component mount
  React.useEffect(() => {
    if (!user?.id) return

    const loadAgencyProfile = async () => {
      console.log('Loading agency profile for user:', user.id)
      const { data, error } = await supabase
        .from('agencies')
        .select('*')
        .eq('user_id', user.id)
        .single()

      if (error) {
        console.error('Error loading agency profile:', error)
        if (error.code === 'PGRST116') {
          toast.error('No agency profile found. Please contact support.')
        } else {
          toast.error('Failed to load agency profile: ' + error.message)
        }
      } else {
        console.log('Agency profile loaded:', data)
        setAgencyProfile(data)
      }
    }

    loadAgencyProfile()
  }, [user])

  // Pricing state
  const [dailyPrice, setDailyPrice] = useState("")
  const [weeklyPrice, setWeeklyPrice] = useState("")
  const [monthlyPrice, setMonthlyPrice] = useState("")

  const [features, setFeatures] = useState<Record<string, string | boolean>>({})

  const [selectedColor, setSelectedColor] = useState("")

  // Add state for all required fields
  const [brand, setBrand] = useState("");
  const [model, setModel] = useState("");
  const [year, setYear] = useState("");
  const [licensePlate, setLicensePlate] = useState("");
  const [seats, setSeats] = useState("");
  const [doors, setDoors] = useState("");
  const [transmission, setTransmission] = useState("");
  const [location, setLocation] = useState("");
  const [mileage, setMileage] = useState("");
  const [deposit, setDeposit] = useState("");
  const [description, setDescription] = useState("");

  // Add state for required fields
  const [fuelType, setFuelType] = useState("");
  const [bodyType, setBodyType] = useState("");
  const [gpsDeviceId, setGpsDeviceId] = useState("");

  const handleImageUpload = (urls: string[]) => {
    setCarImages([...carImages, ...urls])
  }

  const handleRemoveImage = (index: number) => {
    const updatedImages = [...carImages]
    updatedImages.splice(index, 1)
    setCarImages(updatedImages)
  }

  const handleFeatureChange = (idx: string, field: string, val: string | boolean) => {
    setFeatures(features => ({ ...features, [field]: val }))
  }

  const handleFeatureIcon = (idx: string, file: File | null) => {
    if (!file) return
    const url = URL.createObjectURL(file)
    setFeatures(features => ({ ...features, [idx]: url }))
  }

  const addFeature = () => setFeatures({ ...features, icon: '' })

  const removeFeature = (idx: string) => {
    const { [idx]: _, ...rest } = features
    setFeatures(rest)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Validate all required fields
    if (!brand || !model || !year || !licensePlate || !seats || !doors || !fuelType || !transmission || !selectedColor || !location || !mileage || !deposit || !description || !dailyPrice || !bodyType) {
      toast.error("Please fill in all required fields.")
      setIsSubmitting(false)
      return
    }

    if (!agencyProfile?.id) {
      toast.error("Agency profile not loaded. Please try again.")
      setIsSubmitting(false)
      return
    }

    // Map features to database format
    const mappedFeatures = Object.keys(features).filter(key => features[key]).map(key => {
      switch (key) {
        case 'ac': return 'air_conditioning'
        case 'bluetooth': return 'bluetooth'
        case 'airbags': return 'airbags'
        case 'gps': return 'gps'
        case 'usbCharger': return 'usb_charging'
        case 'sunroof': return 'sunroof'
        default: return key
      }
    })

    const newCar = {
      agency_id: agencyProfile.id,
      brand,
      model,
      year: parseInt(year, 10),
      license_plate: licensePlate,
      seats: parseInt(seats, 10),
      doors: parseInt(doors, 10),
      color: selectedColor,
      fuel_type: fuelType.toLowerCase(), // Ensure lowercase for database constraint
      transmission,
      body_type: bodyType,
      address: location,
      mileage: parseInt(mileage, 10),
      security_deposit: parseFloat(deposit),
      description,
      daily_rate: parseFloat(dailyPrice),
      weekly_rate: weeklyPrice ? parseFloat(weeklyPrice) : null,
      monthly_rate: monthlyPrice ? parseFloat(monthlyPrice) : null,
      insurance_info: { type: insuranceType },
      features: mappedFeatures,
      images: carImages,
      gps_device_id: features.gps ? gpsDeviceId || null : null,
      status: 'available'
    }
    const { error } = await supabase.from('cars').insert(newCar)
    if (error) {
      toast.error(error.message)
    } else {
      toast.success("Car added successfully")
      router.push("/agency/dashboard?tab=cars")
    }
    setIsSubmitting(false)
  }

  return (
    <AuthGuard requiredRole="agency">
      <div className="container py-10">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Add New Car</h1>
          <Button variant="outline" size="sm" asChild>
            <Link href="/agency/dashboard?tab=cars">Back to Dashboard</Link>
          </Button>
        </div>

        <form onSubmit={handleSubmit}>
          <Card>
            <CardHeader>
              <CardTitle>Car Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="make">Brand</Label>
                  <Select required value={brand} onValueChange={setBrand}>
                    <SelectTrigger id="make">
                      <SelectValue placeholder="Select a brand" />
                    </SelectTrigger>
                    <SelectContent>
                      {CAR_BRANDS.map(brand => (
                        <SelectItem key={brand} value={brand}>{brand}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="model">Model</Label>
                  <Input id="model" placeholder="e.g. Corolla" required value={model} onChange={e => setModel(e.target.value)} />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="color">Color</Label>
                  <div id="color">
                    <ColorPicker value={selectedColor} onChange={setSelectedColor} />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="year">Year</Label>
                  <Select required value={year} onValueChange={setYear}>
                    <SelectTrigger id="year">
                      <SelectValue placeholder="Select year" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 30 }, (_, i) => `${2024 - i}`).map(year => (
                        <SelectItem key={year} value={year}>{year}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="licensePlate">License Plate</Label>
                  <Input id="licensePlate" placeholder="e.g. 123456-A-12" required value={licensePlate} onChange={e => setLicensePlate(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="seats">Number of Seats</Label>
                  <Select required value={seats} onValueChange={setSeats}>
                    <SelectTrigger id="seats">
                      <SelectValue placeholder="Select seats" />
                    </SelectTrigger>
                    <SelectContent>
                      {[2, 4, 5, 7, 8, 9].map(num => (
                        <SelectItem key={num} value={num.toString()}>{num} seats</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="doors">Number of Doors</Label>
                  <Select required value={doors} onValueChange={setDoors}>
                    <SelectTrigger id="doors">
                      <SelectValue placeholder="Select doors" />
                    </SelectTrigger>
                    <SelectContent>
                      {[2, 3, 4, 5].map(num => (
                        <SelectItem key={num} value={num.toString()}>{num} doors</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="mileage">Mileage (km)</Label>
                  <Input id="mileage" type="number" placeholder="e.g. 15000" required value={mileage} onChange={e => setMileage(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="deposit">Security Deposit (MAD)</Label>
                  <Input id="deposit" type="number" placeholder="e.g. 5000" required value={deposit} onChange={e => setDeposit(e.target.value)} />
                  <p className="text-sm text-muted-foreground">Amount required as security deposit from the renter</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Select required value={location} onValueChange={setLocation}>
                    <SelectTrigger id="location">
                      <SelectValue placeholder="Select location" />
                    </SelectTrigger>
                    <SelectContent>
                      {moroccanCities.map(city => (
                        <SelectItem key={city} value={city}>{city}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="transmission">Transmission</Label>
                  <Select required value={transmission} onValueChange={setTransmission}>
                    <SelectTrigger id="transmission">
                      <SelectValue placeholder="Select transmission" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="automatic">Automatic</SelectItem>
                      <SelectItem value="manual">Manual</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Pricing Section */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-primary" />
                  <h3 className="text-lg font-medium">Pricing Options</h3>
                </div>

                <Card>
                  <CardContent className="pt-6">
                    <Tabs defaultValue="daily" className="w-full">
                      <TabsList className="grid grid-cols-3 mb-4">
                        <TabsTrigger value="daily">Daily Rate</TabsTrigger>
                        <TabsTrigger value="weekly">Weekly Rate</TabsTrigger>
                        <TabsTrigger value="monthly">Monthly Rate</TabsTrigger>
                      </TabsList>

                      <TabsContent value="daily">
                        <div className="space-y-2">
                          <Label htmlFor="daily-price">Daily Price (MAD)</Label>
                          <Input
                            id="daily-price"
                            type="number"
                            placeholder="e.g. 750"
                            value={dailyPrice}
                            onChange={(e) => setDailyPrice(e.target.value)}
                            required
                          />
                          <p className="text-sm text-muted-foreground">Price per day in Moroccan Dirhams</p>
                        </div>
                      </TabsContent>

                      <TabsContent value="weekly">
                        <div className="space-y-2">
                          <Label htmlFor="weekly-price">Weekly Price (MAD)</Label>
                          <Input
                            id="weekly-price"
                            type="number"
                            placeholder="e.g. 4500"
                            value={weeklyPrice}
                            onChange={(e) => setWeeklyPrice(e.target.value)}
                          />
                          <p className="text-sm text-muted-foreground">Price per week in Moroccan Dirhams</p>
                        </div>
                      </TabsContent>

                      <TabsContent value="monthly">
                        <div className="space-y-2">
                          <Label htmlFor="monthly-price">Monthly Price (MAD)</Label>
                          <Input
                            id="monthly-price"
                            type="number"
                            placeholder="e.g. 18000"
                            value={monthlyPrice}
                            onChange={(e) => setMonthlyPrice(e.target.value)}
                          />
                          <p className="text-sm text-muted-foreground">Price per month in Moroccan Dirhams</p>
                        </div>
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </div>

              {/* Insurance Options */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-primary" />
                  <Label className="text-base font-medium">Insurance Type</Label>
                </div>

                <RadioGroup
                  defaultValue="basic"
                  value={insuranceType}
                  onValueChange={setInsuranceType}
                  className="grid grid-cols-1 md:grid-cols-3 gap-4"
                >
                  <div className="flex items-start space-x-2 border p-4 rounded-md hover:border-primary cursor-pointer">
                    <RadioGroupItem value="basic" id="basic" className="mt-1" />
                    <div className="grid gap-1">
                      <Label htmlFor="basic" className="font-medium">
                        Basic Insurance
                      </Label>
                      <p className="text-sm text-muted-foreground">Covers third-party liability only</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2 border p-4 rounded-md hover:border-primary cursor-pointer">
                    <RadioGroupItem value="comprehensive" id="comprehensive" className="mt-1" />
                    <div className="grid gap-1">
                      <Label htmlFor="comprehensive" className="font-medium">
                        Comprehensive
                      </Label>
                      <p className="text-sm text-muted-foreground">Covers damage to the vehicle and third-party</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2 border p-4 rounded-md hover:border-primary cursor-pointer">
                    <RadioGroupItem value="premium" id="premium" className="mt-1" />
                    <div className="grid gap-1">
                      <Label htmlFor="premium" className="font-medium">
                        Premium
                      </Label>
                      <p className="text-sm text-muted-foreground">Full coverage with zero deductible</p>
                    </div>
                  </div>
                </RadioGroup>
              </div>

              {/* Car specifications */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="body-type">Body Type</Label>
                  <Select required value={bodyType} onValueChange={setBodyType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select body type" />
                    </SelectTrigger>
                    <SelectContent>
                      {bodyTypeOptions.map((type) => (
                        <SelectItem key={type} value={type}>{type}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fuel-type">Fuel Type</Label>
                  <Select required value={fuelType} onValueChange={setFuelType}>
                    <SelectTrigger id="fuel-type">
                      <SelectValue placeholder="Select fuel type" />
                    </SelectTrigger>
                    <SelectContent>
                      {fuelTypeOptions.map((type) => (
                        <SelectItem key={type} value={type}>{type.charAt(0).toUpperCase() + type.slice(1)}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* GPS Device ID - only show if GPS feature is selected */}
              {features.gps && (
                <div className="space-y-2">
                  <Label htmlFor="gps-device-id">GPS Device ID (Optional)</Label>
                  <Input
                    id="gps-device-id"
                    placeholder="Enter GPS device ID"
                    value={gpsDeviceId}
                    onChange={e => setGpsDeviceId(e.target.value)}
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea id="description" placeholder="Describe your car..." className="min-h-[120px]" required value={description} onChange={e => setDescription(e.target.value)} />
              </div>

              <div className="space-y-2">
                <Label>Car Images</Label>
                <FileUpload
                  bucket="car-images"
                  folder="cars"
                  onUploadComplete={handleImageUpload}
                  multiple={true}
                  accept="image/*"
                  maxFiles={5}
                  maxSize={5}
                />
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-primary" />
                  <h3 className="text-lg font-medium">Car Features</h3>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  {availableFeatures.slice(0, 9).map((feature, idx) => (
                    <div key={feature.key} className="flex items-center gap-4 mb-2">
                      <Image src={feature.icon} alt={feature.name} width={32} height={32} />
                      <span className="w-32">{feature.name}</span>
                      {feature.type === 'boolean' && (
                        <input type="checkbox" checked={!!features[feature.key]} onChange={e => setFeatures(f => ({ ...f, [feature.key]: e.target.checked }))} />
                      )}
                      {feature.type === 'select' && (
                        <select value={features[feature.key] as string || ''} onChange={e => setFeatures(f => ({ ...f, [feature.key]: e.target.value }))} className="border rounded px-2 py-1">
                          <option value="">Select</option>
                          {feature.options?.map((opt: string) => <option key={opt} value={opt}>{opt}</option>)}
                        </select>
                      )}
                      {feature.type === 'input' && (
                        <input
                          type="text"
                          {...('placeholder' in feature && typeof feature.placeholder === 'string' ? { placeholder: feature.placeholder } : {})}
                          value={features[feature.key] as string || ''}
                          onChange={e => setFeatures(f => ({ ...f, [feature.key]: e.target.value }))}
                          className="border rounded px-2 py-1"
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" asChild>
                <Link href="/agency/dashboard?tab=cars">Cancel</Link>
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Adding Car..." : "Add Car"}
              </Button>
            </CardFooter>
          </Card>
        </form>
      </div>
    </AuthGuard>
  )
}
