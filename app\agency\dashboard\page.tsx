"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Edit,
  Info,
  Plus,
  Trash,
  Car,
  Calendar,
  DollarSign,
  Users,
  Star,
  ThumbsUp,
  ThumbsDown,
  CheckCircle,
  Upload,
  X,
  LayoutDashboard,
  Settings,
  FileText,
  MessageSquare,
  AreaChart,
  BadgePercent,
  User,
  TrendingUp,
  MapPin,
  Clock,
  Eye,
  Filter,
  Search,
  Download,
  RefreshCw,
  BarChart3,
  PieChart,
  Activity,
  Zap,
  Shield,
  Award,
  MessageCircle,
  Instagram,
  Facebook,
  Mail,
  Phone,
  Globe,
} from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { AuthGuard } from "@/components/features/auth/auth-guard"
import { useI18n } from "@/i18n/i18n-provider"
import { DashboardLayout } from "@/components/layouts/dashboard-layout"
import { useRouter, useSearchParams } from "next/navigation"
import { DatePickerWithRange } from "@/components/shared/date-range-picker"
import { addDays } from "date-fns"
import { Select, SelectItem, SelectTrigger, SelectValue, SelectContent } from "@/components/ui/select"
import DatePicker from "react-datepicker"
import "react-datepicker/dist/react-datepicker.css"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "@/components/ui/dialog"
import { useBookings } from '@/contexts/bookings-context'
import type { Booking } from '@/contexts/bookings-context'
import { useAuth } from '@/contexts/auth-context'
import { DateRange } from "react-day-picker"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { supabase } from '@/lib/supabase/client'

interface CarType {
  id: string
  brand: string
  make?: string // Add make as optional for backward compatibility
  model: string
  year: number
  transmission: string
  daily_rate: number
  mileage: number
  security_deposit: number
  color: string
  address?: string
  location?: any // Add location property for GPS functionality
  fuel_type?: string
  seats?: number
  doors?: number
  status?: string
  description?: string
  features?: string[]
  images?: string[]
}

interface BookingRequest {
  id: string
  carId: string
  startDate: string
  endDate: string
  customerName: string
  customerPhone?: string
  customerEmail?: string
  status: "pending" | "accepted" | "rejected"
  totalAmount?: number
  deposit?: number // Add deposit property
  pickupLocation?: string
  dropoffLocation?: string
  notes?: string
}

interface Review {
  id: string
  customerName: string
  customerAvatar: string
  rating: number
  date: string
  comment: string
  status: "pending" | "approved" | "rejected"
}

const moroccanCities = [
  "Agadir", "Al Hoceima", "Asilah", "Azrou", "Beni Mellal", "Berrechid", "Boujdour", "Casablanca", "Chefchaouen", "Dakhla",
  "El Jadida", "Errachidia", "Essaouira", "Fes", "Guelmim", "Ifrane", "Kenitra", "Khemisset", "Khouribga", "Laayoune",
  "Larache", "Marrakech", "Martil", "Meknes", "Mohammedia", "Nador", "Ouarzazate", "Oujda", "Rabat", "Safi",
  "Sale", "Settat", "Sidi Ifni", "Tangier", "Taza", "Tetouan", "Tiznit"
].sort((a, b) => a.localeCompare(b))
const timeOptions = Array.from({ length: 24 }, (_, i) => `${i.toString().padStart(2, "0")}:00`);

const sidebarNavItems = [
  {
    title: "Cars",
    href: "/agency/dashboard?tab=cars",
    icon: <Car className="h-4 w-4" />,
  },
  {
    title: "Bookings",
    href: "/agency/dashboard?tab=bookings",
    icon: <Calendar className="h-4 w-4" />,
  },
  {
    title: "Reviews",
    href: "/agency/dashboard?tab=reviews",
    icon: <MessageSquare className="h-4 w-4" />,
  },
  {
    title: "GPS Tracking",
    href: "/agency/dashboard?tab=gps",
    icon: <FileText className="h-4 w-4" />,
  },
  {
    title: "Coupons",
    href: "/agency/dashboard?tab=coupons",
    icon: <DollarSign className="h-4 w-4" />,
  },
  {
    title: "Analytics",
    href: "/agency/dashboard?tab=analytics",
    icon: <AreaChart className="h-4 w-4" />,
  },
  {
    title: "Settings",
    href: "/agency/dashboard?tab=settings",
    icon: <Settings className="h-4 w-4" />,
  },
]

export default function AgencyDashboardPage() {
  // Move ALL hooks to the top, before any conditional logic or return
  const { t } = useI18n()
  const router = useRouter()
  const searchParams = useSearchParams()
  const tab = searchParams.get("tab") || "cars"
  const message = searchParams.get("message")
  const [selectedCity, setSelectedCity] = useState<string>("")
  const [selectedCities, setSelectedCities] = useState<string[]>([])
  const { user, isAuthenticated, isLoading } = useAuth()
  const [cars, setCars] = useState<CarType[]>([])
  const [loadingCars, setLoadingCars] = useState(true)
  const [carsError, setCarsError] = useState<string | null>(null)
  const { addBooking, deleteBooking, updateBooking } = useBookings()
  const [bookings, setBookings] = useState<Booking[]>([])
  const [reviews, setReviews] = useState<Review[]>([])
  const [loadingReviews, setLoadingReviews] = useState(true)
  const [reviewsError, setReviewsError] = useState<string | null>(null)
  const [bookingRequests, setBookingRequests] = useState<BookingRequest[]>([])
  const [loadingBookingRequests, setLoadingBookingRequests] = useState(true)
  const [bookingRequestsError, setBookingRequestsError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [bookingId, setBookingId] = useState("")
  const [showVerification, setShowVerification] = useState(false)
  const [showCreateBookingModal, setShowCreateBookingModal] = useState(false)
  const [carToDelete, setCarToDelete] = useState<string | null>(null)
  const [newBooking, setNewBooking] = useState({
    customerName: "",
    customerPhone: "",
    customerEmail: "",
    carId: "",
    deposit: 0,
    startDate: "",
    endDate: "",
    pickupLocation: "",
    dropoffLocation: "",
    totalAmount: 0,
    notes: ""
  })

  // Load bookings from database
  const loadBookings = async () => {
    if (!agencyProfile?.id) return

    try {
      setLoadingBookings(true)
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          *,
          cars (
            brand,
            model,
            daily_rate
          )
        `)
        .eq('agency_id', agencyProfile.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error loading bookings:', error)
      } else {
        // Map database bookings to component format
        const mappedBookings = data.map(booking => ({
          id: booking.id,
          carId: booking.car_id,
          customerName: booking.guest_name || `User ${booking.user_id?.split('-')[0]}`,
          startDate: booking.start_date,
          endDate: booking.end_date,
          status: booking.status,
          totalAmount: booking.total_price,
          pickupLocation: booking.pickup_location,
          dropoffLocation: booking.return_location,
          notes: booking.special_requests
        }))
        setBookings(mappedBookings)
      }
    } catch (error) {
      console.error('Error loading bookings:', error)
    } finally {
      setLoadingBookings(false)
    }
  }

  // Auto-calculate total amount when dates or car changes
  useEffect(() => {
    if (newBooking.startDate && newBooking.endDate && newBooking.carId) {
      const startDate = new Date(newBooking.startDate)
      const endDate = new Date(newBooking.endDate)
      const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))

      const selectedCar = cars.find(car => car.id === newBooking.carId)
      if (selectedCar && days > 0) {
        const totalAmount = selectedCar.daily_rate * days
        setNewBooking(prev => ({ ...prev, totalAmount }))
      }
    }
  }, [newBooking.startDate, newBooking.endDate, newBooking.carId, cars])

  // Load bookings when agency profile is available
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null)
  const [bookingDetailsModalOpen, setBookingDetailsModalOpen] = useState(false)
  const [enlargedImage, setEnlargedImage] = useState<string | null>(null)
  const [selectedCarForGPS, setSelectedCarForGPS] = useState<string>("")
  const [mapCenter, setMapCenter] = useState({ lat: 31.7917, lng: -7.0926 })
  const [agencyName, setAgencyName] = useState<string>("")
  const [agencyBookings, setAgencyBookings] = useState<Booking[]>([])
  const [loadingBookings, setLoadingBookings] = useState(true)
  const [bookingsError, setBookingsError] = useState<string | null>(null)
  const [agencyProfile, setAgencyProfile] = useState<any>(null)

  useEffect(() => {
    if (agencyProfile?.id) {
      loadBookings()
    }
  }, [agencyProfile?.id])
  const [loadingAgencyProfile, setLoadingAgencyProfile] = useState(true)
  const [agencyProfileError, setAgencyProfileError] = useState<string | null>(null)
  const [editingProfile, setEditingProfile] = useState(false)
  const [profileForm, setProfileForm] = useState<any>({})
  const [agencySettings, setAgencySettings] = useState({
    rental_terms: '',
    whatsapp: '',
    instagram: '',
    facebook: '',
    email_notifications: true,
    sms_notifications: false,
    review_notifications: true,
    operating_hours_start: '08:00',
    operating_hours_end: '18:00',
    min_driver_age: 21,
    min_rental_period: 1,
    free_cancellation_hours: 48,
    fuel_policy: 'full_to_full',
    additional_terms: '',
    contract_url: ''
  })

  useEffect(() => {
    if (!user || !agencyProfile?.id) return
    setLoadingCars(true)
    setCarsError(null)
    supabase
      .from('cars')
      .select('*')
      .eq('agency_id', agencyProfile.id)
      .then(({ data, error }) => {
        if (error) setCarsError(error.message)
        else setCars(data || [])
        setLoadingCars(false)
      })
  }, [user, agencyProfile])

  useEffect(() => {
    if (message === "complete-registration") {
      toast.success("Welcome to your agency dashboard! Complete your profile to get started.")
    }
  }, [message])

  useEffect(() => {
    if (!user || !agencyProfile?.id) return
    setLoadingBookings(true)
    setBookingsError(null)
    supabase
      .from('bookings')
      .select('*')
      .eq('agency_id', agencyProfile.id)
      .then(({ data, error }) => {
        if (error) setBookingsError(error.message)
        else setAgencyBookings(data || [])
        setLoadingBookings(false)
      })
  }, [user, agencyProfile?.id])

  useEffect(() => {
    if (!user) return
    setLoadingAgencyProfile(true)
    setAgencyProfileError(null)
    supabase
      .from('agencies')
      .select('*')
      .eq('user_id', user.id)
      .single()
      .then(({ data, error }) => {
        if (error) setAgencyProfileError(error.message)
        else {
          setAgencyProfile(data)
          setProfileForm(data)
          // Load agency settings
          setAgencySettings({
            rental_terms: data.rental_terms || '',
            whatsapp: data.whatsapp || '',
            instagram: data.instagram || '',
            facebook: data.facebook || '',
            email_notifications: data.email_notifications ?? true,
            sms_notifications: data.sms_notifications ?? false,
            review_notifications: data.review_notifications ?? true,
            operating_hours_start: data.operating_hours_start || '08:00',
            operating_hours_end: data.operating_hours_end || '18:00',
            min_driver_age: data.min_driver_age || 21,
            min_rental_period: data.min_rental_period || 1,
            free_cancellation_hours: data.free_cancellation_hours || 48,
            fuel_policy: data.fuel_policy || 'full_to_full',
            additional_terms: data.additional_terms || '',
            contract_url: data.contract_url || ''
          })

          // Load selected cities
          if (data.location) {
            setSelectedCities(data.location.split(','))
          }
        }
        setLoadingAgencyProfile(false)
      })
  }, [user])

  // Load reviews from database
  useEffect(() => {
    if (!agencyProfile?.id) return
    setLoadingReviews(true)
    setReviewsError(null)
    supabase
      .from('reviews')
      .select(`
        *,
        users!inner(first_name, last_name, avatar_url),
        bookings!inner(cars!inner(agency_id))
      `)
      .eq('bookings.cars.agency_id', agencyProfile.id)
      .order('created_at', { ascending: false })
      .then(({ data, error }) => {
        if (error) {
          setReviewsError(error.message)
          console.error('Error loading reviews:', error)
        } else {
          const formattedReviews = data?.map(review => ({
            id: review.id,
            customerName: `${review.users.first_name} ${review.users.last_name}`,
            customerAvatar: review.users.avatar_url || "/placeholder.svg?height=40&width=40",
            rating: review.rating,
            date: new Date(review.created_at).toLocaleDateString(),
            comment: review.comment,
            status: review.is_approved ? "approved" : "pending"
          })) || []
          setReviews(formattedReviews)
        }
        setLoadingReviews(false)
      })
  }, [agencyProfile?.id])

  // Load booking requests from database
  useEffect(() => {
    if (!agencyProfile?.id) return
    setLoadingBookingRequests(true)
    setBookingRequestsError(null)
    supabase
      .from('bookings')
      .select(`
        *,
        users!inner(first_name, last_name),
        cars!inner(brand, model, agency_id)
      `)
      .eq('cars.agency_id', agencyProfile.id)
      .in('status', ['pending', 'confirmed', 'cancelled'])
      .order('created_at', { ascending: false })
      .then(({ data, error }) => {
        if (error) {
          setBookingRequestsError(error.message)
          console.error('Error loading booking requests:', error)
        } else {
          const formattedRequests = data?.map(booking => ({
            id: booking.id,
            carId: booking.car_id,
            startDate: booking.start_date,
            endDate: booking.end_date,
            customerName: `${booking.users.first_name} ${booking.users.last_name}`,
            status: booking.status === 'confirmed' ? 'accepted' : booking.status === 'cancelled' ? 'rejected' : 'pending'
          })) || []
          setBookingRequests(formattedRequests)
        }
        setLoadingBookingRequests(false)
      })
  }, [agencyProfile])

  // Replace deleteCar to delete from Supabase
  const deleteCar = async (carId: string) => {
    setLoadingCars(true)
    setCarsError(null)
    const { error } = await supabase
      .from('cars')
      .delete()
      .eq('id', carId)
    if (error) {
      setCarsError(error.message)
      toast.error("Failed to delete car: " + error.message)
    } else {
      setCars(prev => prev.filter(car => car.id !== carId))
      toast.success("Car deleted successfully")
    }
    setLoadingCars(false)
    setCarToDelete(null)
  }

  const confirmDeleteCar = () => {
    if (carToDelete) {
      deleteCar(carToDelete)
    }
  }

  const handleSaveSettings = async () => {
    try {
      if (!agencyProfile?.id) {
        toast.error('Agency profile not loaded. Please refresh the page and try again.')
        console.error('Agency profile missing:', { agencyProfile, user })
        return
      }

      // Get form values
      const rentalTermsElement = document.getElementById('rental-terms') as HTMLTextAreaElement
      const whatsappElement = document.getElementById('whatsapp-link') as HTMLInputElement
      const instagramElement = document.getElementById('instagram-link') as HTMLInputElement
      const facebookElement = document.getElementById('facebook-link') as HTMLInputElement
      const emailNotificationsElement = document.getElementById('email-notifications') as HTMLInputElement
      const smsNotificationsElement = document.getElementById('sms-notifications') as HTMLInputElement
      const reviewNotificationsElement = document.getElementById('review-notifications') as HTMLInputElement
      const minAgeElement = document.getElementById('min-age') as HTMLInputElement
      const minRentalElement = document.getElementById('min-rental') as HTMLInputElement
      const cancellationHoursElement = document.getElementById('cancellation-hours') as HTMLInputElement
      const fuelPolicyElement = document.getElementById('fuel-policy') as HTMLSelectElement
      const additionalTermsElement = document.getElementById('additional-terms') as HTMLTextAreaElement

      // Combine profile form data with settings
      const updatedData = {
        ...profileForm,
        rental_terms: rentalTermsElement?.value || '',
        whatsapp: whatsappElement?.value || '',
        instagram: instagramElement?.value || '',
        facebook: facebookElement?.value || '',
        email_notifications: emailNotificationsElement?.checked ?? true,
        sms_notifications: smsNotificationsElement?.checked ?? false,
        review_notifications: reviewNotificationsElement?.checked ?? true,
        operating_hours_start: agencySettings.operating_hours_start,
        operating_hours_end: agencySettings.operating_hours_end,
        min_driver_age: minAgeElement?.value ? parseInt(minAgeElement.value) : 21,
        min_rental_period: minRentalElement?.value ? parseInt(minRentalElement.value) : 1,
        free_cancellation_hours: cancellationHoursElement?.value ? parseInt(cancellationHoursElement.value) : 48,
        fuel_policy: fuelPolicyElement?.value || 'full_to_full',
        additional_terms: additionalTermsElement?.value || '',
        // Location data
        location: selectedCities.length > 0 ? selectedCities.join(',') : null,
      }

      // Save to database
      const { error } = await supabase
        .from('agencies')
        .update(updatedData)
        .eq('id', agencyProfile.id)

      if (error) {
        console.error('Error saving settings:', error)
        toast.error('Failed to save settings: ' + error.message)
        return
      }

      // Update local state
      setAgencySettings((prev: any) => ({ ...prev, ...updatedData }))
      setAgencyProfile((prev: any) => ({ ...prev, ...updatedData }))
      setProfileForm((prev: any) => ({ ...prev, ...updatedData }))

      toast.success("Settings saved successfully!")
      setShowSuccessModal(true)
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save settings')
    }
  }

  const handleReviewAction = (reviewId: string, action: "approve" | "reject") => {
    setReviews(reviews.map(review =>
      review.id === reviewId
        ? { ...review, status: action === "approve" ? "approved" : "rejected" }
        : review
    ))
    toast.success(`Review ${action}d successfully`)
  }

  const handleCreateBooking = () => {
    setShowCreateBookingModal(true)
  }

  const handleSubmitBooking = async () => {
    if (!newBooking.customerName || !newBooking.carId || !newBooking.startDate || !newBooking.endDate) {
      toast.error("Please fill in all required fields")
      return
    }

    if (!agencyProfile?.id) {
      toast.error("Agency profile not loaded. Please try again.")
      return
    }

    try {
      // Create booking in database
      const bookingData = {
        car_id: newBooking.carId,
        agency_id: agencyProfile.id,
        start_date: newBooking.startDate,
        end_date: newBooking.endDate,
        pickup_location: newBooking.pickupLocation || 'Agency Location',
        return_location: newBooking.dropoffLocation || 'Agency Location',
        total_price: newBooking.totalAmount,
        status: 'pending',
        payment_status: 'pending',
        special_requests: newBooking.notes,
        // Guest booking fields
        guest_name: newBooking.customerName,
        guest_email: newBooking.customerEmail,
        guest_phone: newBooking.customerPhone,
        is_guest_booking: true
      }

      const { data, error } = await supabase
        .from('bookings')
        .insert(bookingData)
        .select()
        .single()

      if (error) {
        console.error('Booking creation error:', error)
        toast.error(`Failed to create booking: ${error.message}`)
        return
      }

      // Refresh bookings list
      loadBookings()

      setShowCreateBookingModal(false)
      setNewBooking({
        customerName: "",
        customerPhone: "",
        customerEmail: "",
        carId: "",
        deposit: 0,
        startDate: "",
        endDate: "",
        pickupLocation: "",
        dropoffLocation: "",
        totalAmount: 0,
        notes: ""
      })
      toast.success("Booking created successfully")
    } catch (error) {
      console.error('Unexpected error creating booking:', error)
      toast.error("An unexpected error occurred while creating the booking")
    }
  }


  const handleViewBookingDetails = (booking: Booking) => {
    setSelectedBooking(booking)
    setBookingDetailsModalOpen(true)
  }

  const handleImageClick = (imageUrl: string) => {
    setEnlargedImage(imageUrl)
  }

  const closeEnlargedImage = () => {
    setEnlargedImage(null)
  }

  const filteredCars = cars.filter(car =>
    car.brand?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    car.model?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    car.id?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = booking.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = filterStatus === "all" || booking.status === filterStatus
    return matchesSearch && matchesStatus
  })

  // Success Modal Component
  const SuccessModal = () => (
    <Dialog open={showSuccessModal} onOpenChange={setShowSuccessModal}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-600" />
            Settings Saved
          </DialogTitle>
          <DialogDescription>
            Your agency settings have been updated successfully.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button onClick={() => setShowSuccessModal(false)} className="w-full">
            Continue
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )

  // Show loading spinner while auth state is loading
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-lg">Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect or block access if not authenticated
  if (!isAuthenticated || !user) {
    if (typeof window !== "undefined") {
      window.location.replace("/auth")
    }
    return null
  }

  // Add booking action handlers
  const handleBookingAction = async (bookingId: string, action: "accept" | "reject") => {
    const newStatus = action === "accept" ? "accepted" : "rejected"
    const { error, data } = await supabase
      .from('bookings')
      .update({ status: newStatus })
      .eq('id', bookingId)
      .select()
      .single()
    if (error) {
      toast.error(`Failed to ${action} booking: ${error.message}`)
    } else {
      setAgencyBookings(prev => prev.map(b => b.id === bookingId ? { ...b, ...data } : b))
      toast.success(`Booking ${action === "accept" ? "accepted" : "rejected"} successfully`)
    }
  }

  // Add car edit and delete actions (delete already implemented)
  const handleEditCar = (carId: string) => {
    router.push(`/agency/dashboard/edit-car/${carId}`)
  }

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setProfileForm({ ...profileForm, [e.target.name]: e.target.value })
  }
  const handleProfileSave = async () => {
    const { error, data } = await supabase
      .from('agencies')
      .update(profileForm)
      .eq('id', agencyProfile.id)
      .select()
      .single()
    if (error) {
      toast.error(`Failed to update profile: ${error.message}`)
    } else {
      setAgencyProfile(data)
      setEditingProfile(false)
      toast.success('Profile updated successfully')
    }
  }

  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file || !agencyProfile?.id) return

    try {
      // Upload to Supabase storage
      const fileExt = file.name.split('.').pop()
      const fileName = `${agencyProfile.id}/logo.${fileExt}`

      const { error: uploadError } = await supabase.storage
        .from('agency-logos')
        .upload(fileName, file, { upsert: true })

      if (uploadError) {
        toast.error('Failed to upload logo: ' + uploadError.message)
        return
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('agency-logos')
        .getPublicUrl(fileName)

      // Update agency profile with new logo URL
      const { error: updateError } = await supabase
        .from('agencies')
        .update({ agency_logo: publicUrl })
        .eq('id', agencyProfile.id)

      if (updateError) {
        toast.error('Failed to update profile: ' + updateError.message)
        return
      }

      // Update local state
      setAgencyProfile((prev: any) => ({ ...prev, agency_logo: publicUrl }))
      setProfileForm((prev: any) => ({ ...prev, agency_logo: publicUrl }))
      toast.success('Logo uploaded successfully!')
    } catch (error) {
      console.error('Error uploading logo:', error)
      toast.error('Failed to upload logo')
    }
  }

  // Test bucket connection
  const testBucketConnection = async () => {
    try {
      const { data: buckets, error } = await supabase.storage.listBuckets()
      console.log('Available buckets:', buckets)
      if (error) {
        console.error('Error listing buckets:', error)
      }

      // Test documents bucket specifically
      const { data: files, error: listError } = await supabase.storage
        .from('documents')
        .list()

      console.log('Documents bucket test:', { files, error: listError })
    } catch (error) {
      console.error('Bucket connection test failed:', error)
    }
  }

  const handleContractUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file || !agencyProfile?.id) {
      toast.error('Please select a file and ensure agency profile is loaded')
      return
    }

    console.log('Starting contract upload:', { fileName: file.name, agencyId: agencyProfile.id })

    // Test bucket connection first
    await testBucketConnection()

    try {
      // Validate file type
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
      if (!allowedTypes.includes(file.type)) {
        toast.error('Please upload a PDF, DOC, or DOCX file')
        return
      }

      // Validate file size (50MB max)
      if (file.size > 50 * 1024 * 1024) {
        toast.error('File size must be less than 50MB')
        return
      }

      // Upload to Supabase storage
      const fileExt = file.name.split('.').pop()
      const fileName = `${agencyProfile.id}/contract.${fileExt}`

      console.log('Uploading to documents bucket:', fileName)

      const { error: uploadError } = await supabase.storage
        .from('documents')
        .upload(fileName, file, { upsert: true })

      if (uploadError) {
        console.error('Upload error:', uploadError)
        toast.error('Failed to upload contract: ' + uploadError.message)
        return
      }

      console.log('File uploaded successfully, getting public URL')

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('documents')
        .getPublicUrl(fileName)

      console.log('Public URL:', publicUrl)

      // Update agency profile with new contract URL
      const { error: updateError } = await supabase
        .from('agencies')
        .update({ contract_url: publicUrl })
        .eq('id', agencyProfile.id)

      if (updateError) {
        console.error('Database update error:', updateError)
        toast.error('Failed to update profile: ' + updateError.message)
        return
      }

      console.log('Database updated successfully')

      // Update local state
      setAgencyProfile((prev: any) => ({ ...prev, contract_url: publicUrl }))
      setProfileForm((prev: any) => ({ ...prev, contract_url: publicUrl }))
      setAgencySettings((prev: any) => ({ ...prev, contract_url: publicUrl }))

      toast.success('Contract uploaded successfully!')

      // Clear the file input
      e.target.value = ''
    } catch (error) {
      console.error('Error uploading contract:', error)
      toast.error('Failed to upload contract: ' + (error as Error).message)
    }
  }

  return (
    <AuthGuard requiredRole="agency">
      <DashboardLayout
        sidebarNavItems={sidebarNavItems}
        title={`Welcome, ${agencyProfile?.name || agencyName}`}
      >
        <div className="flex-1 space-y-6 p-6 md:p-8">




          {/* Email Confirmation Notice */}
          {user && !user.is_verified && (
            <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-yellow-800">
                    Please verify your email address
                  </h3>
                  <p className="text-sm text-yellow-700 mt-1">
                    We've sent a verification email to <strong>{user.email}</strong>. Please check your inbox and click the verification link to complete your account setup.
                  </p>
                </div>
                <Button variant="outline" size="sm" className="text-yellow-800 border-yellow-300 hover:bg-yellow-100">
                  Resend Email
                </Button>
              </div>
            </div>
          )}

          {/* Enhanced Header */}
          <div className="flex flex-col space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  Welcome, {agencyName}
                </h1>
                <p className="text-muted-foreground">
                  Manage your fleet, bookings, and grow your business
                </p>
              </div>
            </div>

            {/* Enhanced Stats Grid */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card className="group hover:shadow-lg transition-all duration-300 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-gray-50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-muted-foreground">Total Cars</p>
                      <p className="text-2xl font-bold">{cars.length}</p>
                      <div className="flex items-center text-sm">
                        <TrendingUp className="mr-1 h-4 w-4 text-green-600" />
                        <span className="text-green-600">+12%</span>
                        <span className="text-muted-foreground ml-1">from last month</span>
                      </div>
                    </div>
                    <div className="p-3 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 text-white group-hover:scale-110 transition-transform duration-300">
                      <Car className="h-6 w-6" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="group hover:shadow-lg transition-all duration-300 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-gray-50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-muted-foreground">Pending Bookings</p>
                      <p className="text-2xl font-bold">{bookings.filter(b => b.status === "pending").length}</p>
                      <div className="flex items-center text-sm">
                        <TrendingUp className="mr-1 h-4 w-4 text-green-600" />
                        <span className="text-green-600">+8%</span>
                        <span className="text-muted-foreground ml-1">from last month</span>
                      </div>
                    </div>
                    <div className="p-3 rounded-full bg-gradient-to-r from-green-500 to-green-600 text-white group-hover:scale-110 transition-transform duration-300">
                      <Calendar className="h-6 w-6" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="group hover:shadow-lg transition-all duration-300 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-gray-50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-muted-foreground">Monthly Revenue</p>
                      <p className="text-2xl font-bold">MAD 45,200</p>
                      <div className="flex items-center text-sm">
                        <TrendingUp className="mr-1 h-4 w-4 text-green-600" />
                        <span className="text-green-600">+15%</span>
                        <span className="text-muted-foreground ml-1">from last month</span>
                      </div>
                    </div>
                    <div className="p-3 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 text-white group-hover:scale-110 transition-transform duration-300">
                      <DollarSign className="h-6 w-6" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="group hover:shadow-lg transition-all duration-300 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-gray-50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-muted-foreground">Average Rating</p>
                      <p className="text-2xl font-bold">4.8</p>
                      <div className="flex items-center text-sm">
                        <TrendingUp className="mr-1 h-4 w-4 text-green-600" />
                        <span className="text-green-600">+0.2</span>
                        <span className="text-muted-foreground ml-1">from last month</span>
                      </div>
                    </div>
                    <div className="p-3 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 text-white group-hover:scale-110 transition-transform duration-300">
                      <Star className="h-6 w-6" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Enhanced Tabs */}
          <Tabs value={tab} className="space-y-6">
            <TabsContent value="cars" className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search cars..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-48">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Cars</SelectItem>
                      <SelectItem value="available">Available</SelectItem>
                      <SelectItem value="rented">Rented</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={() => router.push("/agency/dashboard/add-car")} className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
                  <Plus className="mr-2 h-4 w-4" />
                  Add New Car
                </Button>
              </div>

              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {loadingCars ? (
                  <div className="col-span-full text-center py-10">
                    <div className="w-16 h-16 border-4 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mx-auto"></div>
                    <p className="mt-4 text-lg">Loading cars...</p>
                  </div>
                ) : carsError ? (
                  <div className="col-span-full text-center py-10 text-red-500">
                    Error loading cars: {carsError}
                  </div>
                ) : filteredCars.length === 0 ? (
                  <div className="col-span-full text-center py-10">
                    <MessageSquare className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <p className="text-lg">No cars found matching your criteria.</p>
                    <p className="text-sm text-muted-foreground">Try adjusting your search or filters.</p>
                  </div>
                ) : (
                  filteredCars.map((car) => (
                    <Card key={car.id} className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50">
                      {/* Car Image */}
                      <div className="relative h-48 overflow-hidden rounded-t-lg">
                        <img
                          src={car.images?.[0] || '/placeholder.svg?height=200&width=300'}
                          alt={`${car.brand} ${car.model}`}
                          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        />
                        <div className="absolute top-2 right-2">
                          <Badge variant="secondary" className="bg-white/90 text-black">
                            {car.status || 'Available'}
                          </Badge>
                        </div>
                      </div>

                      <CardHeader className="pb-3">
                        <CardTitle className="text-xl">{car.brand} {car.model}</CardTitle>
                        <CardDescription className="flex items-center space-x-2">
                          <span>{car.year}</span>
                          <span>•</span>
                          <span className="capitalize">{car.transmission}</span>
                          <span>•</span>
                          <span>{car.mileage?.toLocaleString() || 0} km</span>
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">Daily Rate</p>
                            <p className="font-semibold text-lg">د.م {car.daily_rate}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Deposit</p>
                            <p className="font-semibold text-lg">د.م {car.security_deposit?.toLocaleString() || 0}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Fuel Type</p>
                            <p className="font-semibold capitalize">{car.fuel_type || 'N/A'}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Color</p>
                            <p className="font-semibold capitalize">{car.color}</p>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="pt-0">
                        <div className="flex w-full space-x-2">
                          <Button
                            variant="outline"
                            className="flex-1"
                            size="sm"
                            onClick={() => handleEditCar(car.id)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Car
                          </Button>
                          <Button
                            variant="destructive"
                            className="flex-1"
                            size="sm"
                            onClick={() => setCarToDelete(car.id)}
                          >
                            <Trash className="mr-2 h-4 w-4" />
                            Delete
                          </Button>
                        </div>
                      </CardFooter>
                    </Card>
                  ))
                )}
              </div>
            </TabsContent>

            {/* Bookings Tab */}
            <TabsContent value="bookings" className="space-y-4 sm:space-y-6">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-0">
                <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:space-x-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search bookings..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-full sm:w-64"
                    />
                  </div>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-full sm:w-48">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Bookings</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={handleCreateBooking} className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-sm sm:text-base">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Booking
                </Button>
              </div>

              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Calendar className="h-5 w-5" />
                    <span>Recent Bookings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="text-xs sm:text-sm">Booking ID</TableHead>
                        <TableHead className="text-xs sm:text-sm">Customer</TableHead>
                        <TableHead className="text-xs sm:text-sm">Car</TableHead>
                        <TableHead className="text-xs sm:text-sm">Dates</TableHead>
                        <TableHead className="text-xs sm:text-sm">Status</TableHead>
                        <TableHead className="text-xs sm:text-sm">Total</TableHead>
                        <TableHead className="text-xs sm:text-sm">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredBookings.slice(0, 10).map((booking) => (
                        <TableRow key={booking.id} className="hover:bg-gray-50 transition-colors">
                          <TableCell className="font-mono text-xs sm:text-sm">
                            <Badge variant="outline">{booking.id}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Avatar className="h-6 w-6 sm:h-8 sm:w-8">
                                <AvatarImage src="/placeholder.svg?height=32&width=32" />
                                <AvatarFallback className="text-xs">U</AvatarFallback>
                              </Avatar>
                              <span className="text-xs sm:text-sm">{booking.customerName || `User ${booking.id.split('-')[1]}`}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Car className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600" />
                              <span className="text-xs sm:text-sm">Car {booking.carId.split('-')[3]}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-xs sm:text-sm">
                              <div>{new Date(booking.startDate).toLocaleDateString()}</div>
                              <div className="text-muted-foreground">to {new Date(booking.endDate).toLocaleDateString()}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={booking.status === "accepted" ? "default" : booking.status === "pending" ? "secondary" : "destructive"}
                              className="capitalize text-xs"
                            >
                              {booking.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-semibold text-xs sm:text-sm">MAD 2,250</TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewBookingDetails(booking)}
                                className="h-6 w-6 sm:h-8 sm:w-8 p-0"
                              >
                                <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
                              </Button>
                              {booking.status === "pending" && (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-green-600 hover:text-green-700 hover:bg-green-50 h-6 w-6 sm:h-8 sm:w-8 p-0"
                                    onClick={() => handleBookingAction(booking.id, "accept")}
                                  >
                                    <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50 h-6 w-6 sm:h-8 sm:w-8 p-0"
                                    onClick={() => handleBookingAction(booking.id, "reject")}
                                  >
                                    <X className="h-3 w-3 sm:h-4 sm:w-4" />
                                  </Button>
                                </>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Reviews Tab */}
            <TabsContent value="reviews" className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {reviews.map((review) => (
                  <Card key={review.id} className="group hover:shadow-xl transition-all duration-300 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-gray-50">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={review.customerAvatar} />
                            <AvatarFallback>{review.customerName.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                          </Avatar>
                          <div>
                            <CardTitle className="text-lg">{review.customerName}</CardTitle>
                            <div className="flex items-center space-x-1">
                              {[...Array(5)].map((_, i) => (
                                <Star key={i} className={`h-4 w-4 ${i < review.rating ? "text-yellow-400 fill-current" : "text-gray-300"}`} />
                              ))}
                            </div>
                          </div>
                        </div>
                        <Badge
                          variant={review.status === "approved" ? "default" : review.status === "pending" ? "secondary" : "destructive"}
                          className="capitalize"
                        >
                          {review.status}
                        </Badge>
                      </div>
                      <CardDescription className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>{review.date}</span>
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 leading-relaxed">{review.comment}</p>
                    </CardContent>
                    {review.status === "pending" && (
                      <CardFooter className="pt-0">
                        <div className="flex w-full space-x-2">
                          <Button
                            variant="outline"
                            className="flex-1 text-green-600 hover:text-green-700 hover:bg-green-50"
                            size="sm"
                            onClick={() => handleReviewAction(review.id, "approve")}
                          >
                            <ThumbsUp className="mr-2 h-4 w-4" />
                            Approve
                          </Button>
                          <Button
                            variant="outline"
                            className="flex-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                            size="sm"
                            onClick={() => handleReviewAction(review.id, "reject")}
                          >
                            <ThumbsDown className="mr-2 h-4 w-4" />
                            Reject
                          </Button>
                        </div>
                      </CardFooter>
                    )}
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* GPS Tracking Tab */}
            <TabsContent value="gps" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <MapPin className="h-5 w-5" />
                    <span>GPS Fleet Tracking</span>
                  </CardTitle>
                  <CardDescription>
                    Track your vehicles in real-time and monitor their locations across Morocco
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Car Selection */}
                  <div className="space-y-4">
                    <Label htmlFor="car-select-gps">Select Vehicle to Track</Label>
                    <Select
                      value={selectedCarForGPS}
                      onValueChange={(value) => {
                        setSelectedCarForGPS(value)
                        const selectedCar = cars.find(car => car.id === value)
                        if (selectedCar?.location) {
                          setMapCenter(selectedCar.location)
                        }
                      }}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Choose a vehicle from your fleet" />
                      </SelectTrigger>
                      <SelectContent>
                        {cars.map((car) => (
                          <SelectItem key={car.id} value={car.id}>
                            <div className="flex items-center space-x-2">
                              <Car className="h-4 w-4" />
                              <span>{car.brand} {car.model} ({car.year}) - {car.color}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Map Container */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label>Vehicle Location</Label>
                      {selectedCarForGPS && (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                          Live Tracking
                        </Badge>
                      )}
                    </div>

                    <div className="relative h-96 w-full bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 overflow-hidden">
                      {selectedCarForGPS ? (
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-100">
                          {/* Map Placeholder with Car Location */}
                          <div className="relative w-full h-full">
                            {/* Morocco Map Background */}
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="text-center space-y-4">
                                <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto animate-pulse">
                                  <Car className="h-8 w-8 text-white" />
                                </div>
                                <div className="space-y-2">
                                  <p className="text-lg font-semibold text-gray-800">
                                    {cars.find(car => car.id === selectedCarForGPS)?.brand} {cars.find(car => car.id === selectedCarForGPS)?.model}
                                  </p>
                                  <p className="text-sm text-gray-600">
                                    {cars.find(car => car.id === selectedCarForGPS)?.location?.address}
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    Last updated: {new Date().toLocaleTimeString()}
                                  </p>
                                </div>
                              </div>
                            </div>

                            {/* Location Pins */}
                            {cars.map((car) => (
                              <div
                                key={car.id}
                                className={`absolute w-4 h-4 rounded-full border-2 ${car.id === selectedCarForGPS
                                  ? 'bg-red-500 border-white shadow-lg'
                                  : 'bg-gray-400 border-white'
                                  }`}
                                style={{
                                  left: `${Math.random() * 80 + 10}%`,
                                  top: `${Math.random() * 60 + 20}%`,
                                }}
                                title={`${car.brand} ${car.model} - ${car.address || 'N/A'}`}
                              >
                                {car.id === selectedCarForGPS && (
                                  <div className="absolute -top-8 -left-2 bg-red-500 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                                    {car.brand} {car.model}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-center space-y-4">
                            <MapPin className="h-16 w-16 text-gray-400 mx-auto" />
                            <div className="space-y-2">
                              <p className="text-lg font-semibold text-gray-600">Select a Vehicle</p>
                              <p className="text-sm text-gray-500">
                                Choose a vehicle from the dropdown above to view its location
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Vehicle Details */}
                    {selectedCarForGPS && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-600">Vehicle Info</p>
                          <p className="text-lg font-semibold">
                            {cars.find(car => car.id === selectedCarForGPS)?.brand} {cars.find(car => car.id === selectedCarForGPS)?.model}
                          </p>
                          <p className="text-sm text-gray-500">
                            ID: {selectedCarForGPS}
                          </p>
                        </div>
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-600">Current Location</p>
                          <p className="text-lg font-semibold">
                            {cars.find(car => car.id === selectedCarForGPS)?.address || 'Location not available'}
                          </p>
                          <p className="text-sm text-gray-500">
                            GPS: Location tracking available
                          </p>
                        </div>
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-600">Status</p>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            <span className="text-green-600 font-medium">Active</span>
                          </div>
                          <p className="text-sm text-gray-500">
                            Last signal: {new Date().toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Fleet Overview */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Fleet Overview</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {cars.map((car) => (
                          <div
                            key={car.id}
                            className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${selectedCarForGPS === car.id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                              }`}
                            onClick={() => {
                              setSelectedCarForGPS(car.id)
                              if (car.location) {
                                setMapCenter(car.location)
                              }
                            }}
                          >
                            <div className="flex items-center space-x-3">
                              <div className={`w-3 h-3 rounded-full ${selectedCarForGPS === car.id ? 'bg-blue-500' : 'bg-gray-400'
                                }`}></div>
                              <div className="flex-1">
                                <p className="font-medium">{car.brand} {car.model}</p>
                                <p className="text-sm text-gray-500">{car.address || 'N/A'}</p>
                              </div>
                              <Car className="h-4 w-4 text-gray-400" />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Coupons Tab */}
            <TabsContent value="coupons" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <DollarSign className="h-5 w-5" />
                    <span>Manage Coupons</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CouponManager />
                </CardContent>
              </Card>
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="h-5 w-5" />
                    <span>Agency Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Agency Avatar Upload */}
                  <div className="space-y-4">
                    <Label>Agency Logo/Avatar</Label>
                    <div className="flex items-center gap-4">
                      <Avatar className="h-20 w-20 border-2 border-gray-200">
                        <AvatarImage src={agencyProfile?.agency_logo || "/placeholder.svg?height=80&width=80"} alt="Agency Logo" />
                        <AvatarFallback className="text-lg font-semibold">
                          {agencyProfile?.agency_name?.substring(0, 2).toUpperCase() || 'AG'}
                        </AvatarFallback>
                      </Avatar>
                      <div className="space-y-2">
                        <input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          id="avatar-upload"
                          onChange={handleLogoUpload}
                        />
                        <Button
                          variant="outline"
                          onClick={() => document.getElementById('avatar-upload')?.click()}
                          className="flex items-center gap-2"
                        >
                          <Upload className="h-4 w-4" />
                          Upload New Logo
                        </Button>
                        <p className="text-xs text-muted-foreground">
                          Recommended: 200x200px, PNG or JPG format
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="grid gap-6 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="agency-name">Agency Name</Label>
                      <Input
                        id="agency-name"
                        name="agency_name"
                        value={profileForm.agency_name || ''}
                        onChange={handleProfileChange}
                        placeholder="Enter your agency name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="owner-name">Owner Name</Label>
                      <Input
                        id="owner-name"
                        value={`${user?.user_metadata?.first_name || ''} ${user?.user_metadata?.last_name || ''}`.trim() || 'N/A'}
                        disabled
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="agency-email">Email</Label>
                      <Input
                        id="agency-email"
                        type="email"
                        value={agencyProfile?.agency_email || user?.email || ''}
                        onChange={(e) => setProfileForm({ ...profileForm, agency_email: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="agency-phone">Phone</Label>
                      <Input
                        id="agency-phone"
                        value={profileForm?.agency_phone || agencyProfile?.agency_phone || ''}
                        onChange={(e) => setProfileForm({ ...profileForm, agency_phone: e.target.value })}
                        placeholder="+212 6 12 34 56 78"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="agency-description">Agency Description</Label>
                    <Textarea
                      id="agency-description"
                      value={profileForm?.agency_description || agencyProfile?.agency_description || ''}
                      onChange={(e) => setProfileForm({ ...profileForm, agency_description: e.target.value })}
                      placeholder="Describe your agency, services, and what makes you special..."
                      rows={4}
                    />
                  </div>

                  <div className="grid gap-6 md:grid-cols-1">
                    <div className="space-y-2">
                      <Label htmlFor="agency-location">Location</Label>
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="all-over-morocco"
                            checked={selectedCities.includes("all-over-morocco")}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedCities(["all-over-morocco"])
                              } else {
                                setSelectedCities([])
                              }
                            }}
                          />
                          <Label htmlFor="all-over-morocco" className="font-medium">All Over Morocco</Label>
                        </div>

                        {!selectedCities.includes("all-over-morocco") && (
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-48 overflow-y-auto border rounded-md p-3">
                            {moroccanCities.map((city) => (
                              <div key={city} className="flex items-center space-x-2">
                                <Checkbox
                                  id={city.toLowerCase()}
                                  checked={selectedCities.includes(city.toLowerCase())}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      setSelectedCities([...selectedCities, city.toLowerCase()])
                                    } else {
                                      setSelectedCities(selectedCities.filter(c => c !== city.toLowerCase()))
                                    }
                                  }}
                                />
                                <Label htmlFor={city.toLowerCase()} className="text-sm">{city}</Label>
                              </div>
                            ))}
                          </div>
                        )}

                        {selectedCities.length > 0 && (
                          <div className="text-sm text-muted-foreground">
                            Selected: {selectedCities.includes("all-over-morocco")
                              ? "All Over Morocco"
                              : selectedCities.map(city => moroccanCities.find(c => c.toLowerCase() === city) || city).join(", ")
                            }
                          </div>
                        )}
                      </div>
                    </div>
                  </div>



                  {/* Agency Public Profile URL */}
                  <div className="space-y-2">
                    <Label>Your Agency Public Profile</Label>
                    <div className="flex items-center space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <Globe className="h-5 w-5 text-blue-600" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-blue-900">
                          {profileForm.agency_name ?
                            `kriwdrive.com/agency-profile/${profileForm.agency_name.toLowerCase().replace(/\s+/g, '-')}` :
                            'kriwdrive.com/agency-profile/your-agency-name'
                          }
                        </p>
                        <p className="text-xs text-blue-700">
                          This is your public agency profile page where customers can view your cars and information
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const url = profileForm.agency_name ?
                            `${window.location.origin}/agency-profile/${profileForm.agency_name.toLowerCase().replace(/\s+/g, '-')}` :
                            `${window.location.origin}/agency-profile/your-agency-name`
                          navigator.clipboard.writeText(url)
                          toast.success('Agency URL copied to clipboard!')
                        }}
                      >
                        Copy Link
                      </Button>
                    </div>
                  </div>

                  {/* Rental Policy */}
                  <div className="space-y-4">
                    <Label>Rental Policy</Label>
                    <div className="space-y-4">
                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="space-y-2">
                          <Label htmlFor="min-age">Minimum Driver Age</Label>
                          <Input
                            id="min-age"
                            type="number"
                            value={agencySettings.min_driver_age}
                            onChange={(e) => setAgencySettings(prev => ({ ...prev, min_driver_age: parseInt(e.target.value) || 21 }))}
                            min="18"
                            max="25"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="min-rental">Minimum Rental Period (days)</Label>
                          <Input
                            id="min-rental"
                            type="number"
                            value={agencySettings.min_rental_period}
                            onChange={(e) => setAgencySettings(prev => ({ ...prev, min_rental_period: parseInt(e.target.value) || 1 }))}
                            min="1"
                            max="30"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="cancellation-hours">Free Cancellation (hours before pickup)</Label>
                          <Input
                            id="cancellation-hours"
                            type="number"
                            value={agencySettings.free_cancellation_hours}
                            onChange={(e) => setAgencySettings(prev => ({ ...prev, free_cancellation_hours: parseInt(e.target.value) || 48 }))}
                            min="0"
                            max="168"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="fuel-policy">Fuel Policy</Label>
                          <Select
                            value={agencySettings.fuel_policy}
                            onValueChange={(value) => setAgencySettings(prev => ({ ...prev, fuel_policy: value }))}
                          >
                            <SelectTrigger id="fuel-policy">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="full_to_full">Full to Full</SelectItem>
                              <SelectItem value="full_to_empty">Full to Empty</SelectItem>
                              <SelectItem value="prepaid">Prepaid Fuel</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="rental-terms">Rental Terms</Label>
                        <Textarea
                          id="rental-terms"
                          placeholder="Enter your rental terms..."
                          rows={4}
                          value={agencySettings.rental_terms}
                          onChange={(e) => setAgencySettings(prev => ({ ...prev, rental_terms: e.target.value }))}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="additional-terms">Additional Terms & Conditions</Label>
                        <Textarea
                          id="additional-terms"
                          placeholder="Enter any additional terms and conditions..."
                          rows={4}
                          value={agencySettings.additional_terms}
                          onChange={(e) => setAgencySettings(prev => ({ ...prev, additional_terms: e.target.value }))}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Social Media Links */}
                  <div className="space-y-4">
                    <Label>Social Media Links</Label>
                    <div className="grid gap-4 md:grid-cols-3">
                      <div className="space-y-2">
                        <Label htmlFor="whatsapp-link" className="flex items-center gap-2">
                          <MessageCircle className="h-4 w-4 text-green-600" />
                          WhatsApp
                        </Label>
                        <Input
                          id="whatsapp-link"
                          placeholder="+212 6 12 34 56 78"
                          value={agencySettings.whatsapp}
                          onChange={(e) => setAgencySettings(prev => ({ ...prev, whatsapp: e.target.value }))}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="instagram-link" className="flex items-center gap-2">
                          <Instagram className="h-4 w-4 text-pink-600" />
                          Instagram
                        </Label>
                        <Input
                          id="instagram-link"
                          placeholder="@premium_auto_rentals"
                          value={agencySettings.instagram}
                          onChange={(e) => setAgencySettings(prev => ({ ...prev, instagram: e.target.value }))}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="facebook-link" className="flex items-center gap-2">
                          <Facebook className="h-4 w-4 text-blue-600" />
                          Facebook
                        </Label>
                        <Input
                          id="facebook-link"
                          placeholder="Premium Auto Rentals Morocco"
                          value={agencySettings.facebook}
                          onChange={(e) => setAgencySettings(prev => ({ ...prev, facebook: e.target.value }))}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Contract Upload */}
                  <div className="space-y-4">
                    <Label>Agency Contract</Label>
                    <div className="space-y-2">
                      <input
                        type="file"
                        accept=".pdf,.doc,.docx"
                        className="hidden"
                        id="contract-upload"
                        onChange={handleContractUpload}
                      />
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          onClick={() => document.getElementById('contract-upload')?.click()}
                          className="flex items-center gap-2"
                        >
                          <Upload className="h-4 w-4" />
                          Upload Contract
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={testBucketConnection}
                          className="text-xs"
                        >
                          Test Bucket
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Upload your agency contract (PDF, DOC, DOCX). This will be available to customers during booking.
                      </p>
                      {agencySettings.contract_url && (
                        <div className="flex items-center gap-2 p-2 bg-green-50 rounded border border-green-200">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span className="text-sm text-green-800">Contract uploaded</span>
                          <a
                            href={agencySettings.contract_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-700 text-sm"
                          >
                            View Contract
                          </a>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <Label>Business Hours</Label>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>Opening Time</Label>
                        <Select
                          value={agencySettings.operating_hours_start}
                          onValueChange={(value) => setAgencySettings(prev => ({ ...prev, operating_hours_start: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {timeOptions.map((time) => (
                              <SelectItem key={time} value={time}>{time}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label>Closing Time</Label>
                        <Select
                          value={agencySettings.operating_hours_end}
                          onValueChange={(value) => setAgencySettings(prev => ({ ...prev, operating_hours_end: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {timeOptions.map((time) => (
                              <SelectItem key={time} value={time}>{time}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <Label>Notifications</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="email-notifications"
                          checked={agencySettings.email_notifications}
                          onCheckedChange={(checked) => setAgencySettings(prev => ({ ...prev, email_notifications: !!checked }))}
                        />
                        <Label htmlFor="email-notifications">Email notifications for new bookings</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="sms-notifications"
                          checked={agencySettings.sms_notifications}
                          onCheckedChange={(checked) => setAgencySettings(prev => ({ ...prev, sms_notifications: !!checked }))}
                        />
                        <Label htmlFor="sms-notifications">SMS notifications for urgent matters</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="review-notifications"
                          checked={agencySettings.review_notifications}
                          onCheckedChange={(checked) => setAgencySettings(prev => ({ ...prev, review_notifications: !!checked }))}
                        />
                        <Label htmlFor="review-notifications">Notifications for new reviews</Label>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button onClick={handleSaveSettings} className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
                    <Shield className="mr-2 h-4 w-4" />
                    Save Settings
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Success Modal */}
        <SuccessModal />

        {/* Delete Car Confirmation Modal */}
        <Dialog open={!!carToDelete} onOpenChange={() => setCarToDelete(null)}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Trash className="h-6 w-6 text-red-600" />
                Delete Car
              </DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this car? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setCarToDelete(null)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={confirmDeleteCar}
              >
                <Trash className="mr-2 h-4 w-4" />
                Delete Car
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Create Booking Modal */}
        <Dialog open={showCreateBookingModal} onOpenChange={setShowCreateBookingModal}>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Create New Booking
              </DialogTitle>
              <DialogDescription>
                Fill in the customer and booking details below
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* Customer Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Customer Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="customer-name">Customer Name *</Label>
                    <Input
                      id="customer-name"
                      value={newBooking.customerName}
                      onChange={(e) => setNewBooking({ ...newBooking, customerName: e.target.value })}
                      placeholder="Enter customer name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="customer-phone">Phone Number</Label>
                    <Input
                      id="customer-phone"
                      value={newBooking.customerPhone}
                      onChange={(e) => setNewBooking({ ...newBooking, customerPhone: e.target.value })}
                      placeholder="+212 6 12 34 56 78"
                    />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="customer-email">Email Address</Label>
                    <Input
                      id="customer-email"
                      type="email"
                      value={newBooking.customerEmail}
                      onChange={(e) => setNewBooking({ ...newBooking, customerEmail: e.target.value })}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
              </div>

              {/* Booking Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Booking Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="car-select">Select Car *</Label>
                    <Select value={newBooking.carId} onValueChange={(value) => {
                      setNewBooking((prev) => {
                        const selectedCar = cars.find(car => car.id === value)
                        return {
                          ...prev,
                          carId: value,
                          deposit: selectedCar ? selectedCar.security_deposit || 0 : 0
                        }
                      })
                    }}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a car" />
                      </SelectTrigger>
                      <SelectContent>
                        {cars.map((car) => (
                          <SelectItem key={car.id} value={car.id}>
                            {car.brand} {car.model} ({car.year}) - MAD {car.daily_rate}/day
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="deposit">Deposit (MAD)</Label>
                    <Input
                      id="deposit"
                      type="number"
                      value={newBooking.deposit}
                      onChange={(e) => setNewBooking({ ...newBooking, deposit: Number(e.target.value) })}
                      placeholder="0"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="total-amount">Total Amount (MAD)</Label>
                    <Input
                      id="total-amount"
                      type="number"
                      value={newBooking.totalAmount}
                      readOnly
                      className="bg-gray-50"
                      placeholder="Auto-calculated based on car price and days"
                    />
                    <p className="text-xs text-gray-500">
                      Automatically calculated: {newBooking.startDate && newBooking.endDate && newBooking.carId ?
                        `${Math.ceil((new Date(newBooking.endDate).getTime() - new Date(newBooking.startDate).getTime()) / (1000 * 60 * 60 * 24))} days × ${cars.find(car => car.id === newBooking.carId)?.daily_rate || 0} MAD/day` :
                        'Select car and dates to calculate'
                      }
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="start-date">Start Date *</Label>
                    <Input
                      id="start-date"
                      type="date"
                      value={newBooking.startDate}
                      onChange={(e) => setNewBooking({ ...newBooking, startDate: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="end-date">End Date *</Label>
                    <Input
                      id="end-date"
                      type="date"
                      value={newBooking.endDate}
                      onChange={(e) => setNewBooking({ ...newBooking, endDate: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="pickup-location">Pickup Location</Label>
                    <Select value={newBooking.pickupLocation} onValueChange={(value) => setNewBooking({ ...newBooking, pickupLocation: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select pickup location" />
                      </SelectTrigger>
                      <SelectContent>
                        {moroccanCities.map((city) => (
                          <SelectItem key={city} value={city}>{city}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dropoff-location">Dropoff Location</Label>
                    <Select value={newBooking.dropoffLocation} onValueChange={(value) => setNewBooking({ ...newBooking, dropoffLocation: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select dropoff location" />
                      </SelectTrigger>
                      <SelectContent>
                        {moroccanCities.map((city) => (
                          <SelectItem key={city} value={city}>{city}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Additional Notes */}
              <div className="space-y-2">
                <Label htmlFor="booking-notes">Additional Notes</Label>
                <Textarea
                  id="booking-notes"
                  value={newBooking.notes}
                  onChange={(e) => setNewBooking({ ...newBooking, notes: e.target.value })}
                  placeholder="Any special requirements or notes..."
                  rows={3}
                />
              </div>
            </div>

            <DialogFooter className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowCreateBookingModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitBooking}
                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
              >
                <Plus className="mr-2 h-4 w-4" />
                Create Booking
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Booking Details Modal */}
        {selectedBooking && (
          <Dialog open={bookingDetailsModalOpen} onOpenChange={setBookingDetailsModalOpen}>
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Booking Details
                </DialogTitle>
              </DialogHeader>

              <div className="space-y-6">
                {/* Booking Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Booking Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="booking-id">Booking ID</Label>
                      <p>{selectedBooking.id}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="customer-name">Customer Name</Label>
                      <p>{selectedBooking.customerName}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="car-id">Car ID</Label>
                      <p>{selectedBooking.carId}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="start-date">Start Date</Label>
                      <p>{new Date(selectedBooking.startDate).toLocaleDateString()}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="end-date">End Date</Label>
                      <p>{new Date(selectedBooking.endDate).toLocaleDateString()}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="status">Status</Label>
                      <p>{selectedBooking.status}</p>
                    </div>
                  </div>
                </div>

                {/* Additional Details */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Additional Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="total-amount">Total Amount</Label>
                      <p>MAD {selectedBooking.totalAmount?.toLocaleString() || "2,250"}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="pickup-location">Pickup Location</Label>
                      <p>{selectedBooking.pickupLocation || "Agadir International Airport"}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="dropoff-location">Dropoff Location</Label>
                      <p>{selectedBooking.dropoffLocation || "Agadir International Airport"}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="notes">Notes</Label>
                      <p>{selectedBooking.notes || "No additional notes"}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="deposit">Deposit</Label>
                      <p>{selectedBooking.deposit ? `${selectedBooking.deposit} MAD` : 'N/A'}</p>
                    </div>
                  </div>
                </div>

                {/* Images Section */}
                {selectedBooking.images && selectedBooking.images.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">Booking Images</h3>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {selectedBooking.images.map((image, index) => (
                        <div
                          key={index}
                          className="aspect-square rounded-lg overflow-hidden border cursor-pointer hover:opacity-80 transition-opacity"
                          onClick={() => handleImageClick(image)}
                        >
                          <img
                            src={image}
                            alt={`Booking image ${index + 1}`}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.src = "/placeholder.svg?height=200&width=200"
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <DialogFooter className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => setBookingDetailsModalOpen(false)}
                >
                  Close
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {/* Enlarged Image Modal */}
        {enlargedImage && (
          <Dialog open={!!enlargedImage} onOpenChange={() => setEnlargedImage(null)}>
            <DialogContent className="sm:max-w-[90vw] max-h-[90vh] p-0 bg-transparent border-none">
              <div className="relative">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={closeEnlargedImage}
                  className="absolute top-4 right-4 z-10 bg-white/80 hover:bg-white"
                >
                  <X className="h-4 w-4" />
                </Button>
                <img
                  src={enlargedImage}
                  alt="Enlarged booking image"
                  className="w-full h-full object-contain max-h-[80vh] rounded-lg"
                  onError={(e) => {
                    e.currentTarget.src = "/placeholder.svg?height=400&width=600"
                  }}
                />
              </div>
            </DialogContent>
          </Dialog>
        )}
      </DashboardLayout>
    </AuthGuard>
  )
}

function CouponManager() {
  const [coupons, setCoupons] = useState([
    { code: "ABC123", percent: 10, expiry: "2025-12-31" },
    { code: "SUMMER25", percent: 25, expiry: "2025-08-31" },
  ])
  const [newCode, setNewCode] = useState("")
  const [newPercent, setNewPercent] = useState("")
  const [newExpiry, setNewExpiry] = useState("")
  const [error, setError] = useState("")

  function handleAddCoupon(e: React.FormEvent) {
    e.preventDefault()
    if (!newCode || !newPercent || !newExpiry) {
      setError("All fields are required.")
      return
    }
    if (coupons.some(c => c.code === newCode)) {
      setError("Coupon code already exists.")
      return
    }
    setCoupons([
      ...coupons,
      { code: newCode.toUpperCase(), percent: Number(newPercent), expiry: newExpiry }
    ])
    setNewCode("")
    setNewPercent("")
    setNewExpiry("")
    setError("")
  }

  function handleDeleteCoupon(code: string) {
    setCoupons(coupons.filter(c => c.code !== code))
  }

  return (
    <div className="space-y-6">
      <form onSubmit={handleAddCoupon} className="flex flex-col md:flex-row gap-4 items-end">
        <div className="flex-1">
          <Label htmlFor="coupon-code">Code</Label>
          <Input id="coupon-code" value={newCode} onChange={e => setNewCode(e.target.value.toUpperCase())} placeholder="e.g. SPRING10" required />
        </div>
        <div className="flex-1">
          <Label htmlFor="coupon-percent">Percent</Label>
          <Input id="coupon-percent" type="number" min="1" max="100" value={newPercent} onChange={e => setNewPercent(e.target.value)} placeholder="e.g. 10" required />
        </div>
        <div className="flex-1">
          <Label htmlFor="coupon-expiry">Expiry</Label>
          <Input id="coupon-expiry" type="date" value={newExpiry} onChange={e => setNewExpiry(e.target.value)} required />
        </div>
        <Button type="submit" className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">Add Coupon</Button>
      </form>
      {error && <div className="text-red-500 text-sm">{error}</div>}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Code</TableHead>
            <TableHead>Percent</TableHead>
            <TableHead>Expiry</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {coupons.map(coupon => (
            <TableRow key={coupon.code}>
              <TableCell className="font-mono">{coupon.code}</TableCell>
              <TableCell>{coupon.percent}%</TableCell>
              <TableCell>{coupon.expiry}</TableCell>
              <TableCell>
                <Button variant="outline" size="sm" onClick={() => handleDeleteCoupon(coupon.code)} className="text-red-600 hover:text-red-700">Delete</Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
