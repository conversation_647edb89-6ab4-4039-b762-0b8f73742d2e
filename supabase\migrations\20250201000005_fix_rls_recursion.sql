-- Fix RLS infinite recursion by simplifying policies
-- The issue is that admin policies are checking the users table from within the users table

-- =============================================
-- DROP ALL EXISTING POLICIES TO START FRESH
-- =============================================

-- Users policies
DROP POLICY IF EXISTS "Anyone can view user profiles" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Users can create own profile" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;

-- Agencies policies  
DROP POLICY IF EXISTS "Anyone can view approved agencies" ON public.agencies;
DROP POLICY IF EXISTS "Agency owners can view own agency" ON public.agencies;
DROP POLICY IF EXISTS "Agency owners can update own agency" ON public.agencies;
DROP POLICY IF EXISTS "Authenticated users can create agencies" ON public.agencies;
DROP POLICY IF EXISTS "<PERSON><PERSON> can manage all agencies" ON public.agencies;

-- Cars policies
DROP POLICY IF EXISTS "Anyone can view approved agency cars" ON public.cars;
DROP POLICY IF EXISTS "Agency owners can view own cars" ON public.cars;
DROP POLICY IF EXISTS "Agency owners can insert own cars" ON public.cars;
DROP POLICY IF EXISTS "Agency owners can update own cars" ON public.cars;
DROP POLICY IF EXISTS "Agency owners can delete own cars" ON public.cars;
DROP POLICY IF EXISTS "Admins can manage all cars" ON public.cars;

-- Bookings policies
DROP POLICY IF EXISTS "Users can view own bookings" ON public.bookings;
DROP POLICY IF EXISTS "Agency owners can view agency bookings" ON public.bookings;
DROP POLICY IF EXISTS "Authenticated users can create bookings" ON public.bookings;
DROP POLICY IF EXISTS "Agency owners can update agency bookings" ON public.bookings;
DROP POLICY IF EXISTS "Admins can manage all bookings" ON public.bookings;

-- Cities policies
DROP POLICY IF EXISTS "Anyone can view cities" ON public.cities;
DROP POLICY IF EXISTS "Admins can manage cities" ON public.cities;

-- =============================================
-- SIMPLIFIED USERS TABLE POLICIES (NO RECURSION)
-- =============================================

-- Anyone can view user profiles (for public info like names, avatars)
CREATE POLICY "Anyone can view user profiles" ON public.users
    FOR SELECT USING (true);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Users can insert their own profile during registration
CREATE POLICY "Users can create own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Simple admin policy using JWT claims instead of table lookup
CREATE POLICY "Service role can manage all users" ON public.users
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================
-- AGENCIES TABLE POLICIES
-- =============================================

-- Anyone can view approved agencies (for public listings)
CREATE POLICY "Anyone can view approved agencies" ON public.agencies
    FOR SELECT USING (is_approved = true);

-- Agency owners can view their own agency (even if not approved)
CREATE POLICY "Agency owners can view own agency" ON public.agencies
    FOR SELECT USING (auth.uid() = user_id);

-- Agency owners can update their own agency
CREATE POLICY "Agency owners can update own agency" ON public.agencies
    FOR UPDATE USING (auth.uid() = user_id);

-- Authenticated users can create agencies
CREATE POLICY "Authenticated users can create agencies" ON public.agencies
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND 
        auth.uid() = user_id
    );

-- Service role can manage all agencies
CREATE POLICY "Service role can manage all agencies" ON public.agencies
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================
-- CARS TABLE POLICIES
-- =============================================

-- Anyone can view cars from approved agencies
CREATE POLICY "Anyone can view approved agency cars" ON public.cars
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.agencies
            WHERE id = agency_id AND is_approved = true
        )
    );

-- Agency owners can view their own cars
CREATE POLICY "Agency owners can view own cars" ON public.cars
    FOR SELECT USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Agency owners can insert cars for their agency
CREATE POLICY "Agency owners can insert own cars" ON public.cars
    FOR INSERT WITH CHECK (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Agency owners can update their own cars
CREATE POLICY "Agency owners can update own cars" ON public.cars
    FOR UPDATE USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Agency owners can delete their own cars
CREATE POLICY "Agency owners can delete own cars" ON public.cars
    FOR DELETE USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Service role can manage all cars
CREATE POLICY "Service role can manage all cars" ON public.cars
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================
-- BOOKINGS TABLE POLICIES
-- =============================================

-- Users can view their own bookings
CREATE POLICY "Users can view own bookings" ON public.bookings
    FOR SELECT USING (auth.uid() = user_id);

-- Agency owners can view bookings for their cars
CREATE POLICY "Agency owners can view agency bookings" ON public.bookings
    FOR SELECT USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Authenticated users can create bookings
CREATE POLICY "Authenticated users can create bookings" ON public.bookings
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR user_id IS NULL)
    );

-- Agency owners can update bookings for their cars
CREATE POLICY "Agency owners can update agency bookings" ON public.bookings
    FOR UPDATE USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Service role can manage all bookings
CREATE POLICY "Service role can manage all bookings" ON public.bookings
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================
-- CITIES TABLE POLICIES
-- =============================================

-- Anyone can view cities
CREATE POLICY "Anyone can view cities" ON public.cities
    FOR SELECT USING (true);

-- Service role can manage cities
CREATE POLICY "Service role can manage cities" ON public.cities
    FOR ALL USING (auth.role() = 'service_role');
