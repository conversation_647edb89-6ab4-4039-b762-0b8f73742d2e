-- Create storage buckets and set up proper policies

-- Create documents bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
SELECT 'documents', 'documents', true, 52428800, ARRAY['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'documents');

-- Create car-images bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
SELECT 'car-images', 'car-images', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'car-images');

-- Create agency-logos bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
SELECT 'agency-logos', 'agency-logos', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml']
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'agency-logos');

-- Set up storage policies for documents bucket
-- Allow authenticated users to upload documents
CREATE POLICY "Allow authenticated users to upload documents" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'documents' 
  AND auth.role() = 'authenticated'
);

-- Allow public read access to documents
CREATE POLICY "Allow public read access to documents" ON storage.objects
FOR SELECT USING (bucket_id = 'documents');

-- Allow users to update their own documents
CREATE POLICY "Allow users to update their own documents" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'documents' 
  AND auth.role() = 'authenticated'
);

-- Allow users to delete their own documents
CREATE POLICY "Allow users to delete their own documents" ON storage.objects
FOR DELETE USING (
  bucket_id = 'documents' 
  AND auth.role() = 'authenticated'
);

-- Set up storage policies for car-images bucket
-- Allow authenticated users to upload car images
CREATE POLICY "Allow authenticated users to upload car images" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'car-images' 
  AND auth.role() = 'authenticated'
);

-- Allow public read access to car images
CREATE POLICY "Allow public read access to car images" ON storage.objects
FOR SELECT USING (bucket_id = 'car-images');

-- Allow users to update their own car images
CREATE POLICY "Allow users to update their own car images" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'car-images' 
  AND auth.role() = 'authenticated'
);

-- Allow users to delete their own car images
CREATE POLICY "Allow users to delete their own car images" ON storage.objects
FOR DELETE USING (
  bucket_id = 'car-images' 
  AND auth.role() = 'authenticated'
);

-- Set up storage policies for agency-logos bucket
-- Allow authenticated users to upload agency logos
CREATE POLICY "Allow authenticated users to upload agency logos" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'agency-logos' 
  AND auth.role() = 'authenticated'
);

-- Allow public read access to agency logos
CREATE POLICY "Allow public read access to agency logos" ON storage.objects
FOR SELECT USING (bucket_id = 'agency-logos');

-- Allow users to update their own agency logos
CREATE POLICY "Allow users to update their own agency logos" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'agency-logos' 
  AND auth.role() = 'authenticated'
);

-- Allow users to delete their own agency logos
CREATE POLICY "Allow users to delete their own agency logos" ON storage.objects
FOR DELETE USING (
  bucket_id = 'agency-logos' 
  AND auth.role() = 'authenticated'
);
