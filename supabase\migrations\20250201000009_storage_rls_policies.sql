-- Comprehensive Storage RLS Policies
-- This migration creates proper RLS policies for all storage buckets

-- =============================================
-- CAR IMAGES BUCKET POLICIES
-- =============================================

-- Drop existing policies
DROP POLICY IF EXISTS "Car images are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Agency owners can upload car images" ON storage.objects;
DROP POLICY IF EXISTS "Agency owners can update car images" ON storage.objects;
DROP POLICY IF EXISTS "Agency owners can delete their car images" ON storage.objects;

-- Anyone can view car images (public access)
CREATE POLICY "Car images public read" ON storage.objects
    FOR SELECT USING (bucket_id = 'car-images');

-- Agency owners can upload images for their cars
CREATE POLICY "Agency owners can upload car images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'car-images' AND
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM public.agencies a
            JOIN public.cars c ON a.id = c.agency_id
            WHERE a.user_id = auth.uid() 
            AND c.id::text = (storage.foldername(name))[1]
        )
    );

-- Agency owners can update images for their cars
CREATE POLICY "Agency owners can update car images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'car-images' AND
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM public.agencies a
            JOIN public.cars c ON a.id = c.agency_id
            WHERE a.user_id = auth.uid() 
            AND c.id::text = (storage.foldername(name))[1]
        )
    );

-- Agency owners can delete images for their cars
CREATE POLICY "Agency owners can delete car images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'car-images' AND
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM public.agencies a
            JOIN public.cars c ON a.id = c.agency_id
            WHERE a.user_id = auth.uid() 
            AND c.id::text = (storage.foldername(name))[1]
        )
    );

-- =============================================
-- AGENCY LOGOS BUCKET POLICIES
-- =============================================

-- Drop existing policies
DROP POLICY IF EXISTS "Agency logos are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Agency owners can upload their logo" ON storage.objects;

-- Anyone can view agency logos (public access)
CREATE POLICY "Agency logos public read" ON storage.objects
    FOR SELECT USING (bucket_id = 'agency-logos');

-- Agency owners can upload their own logo
CREATE POLICY "Agency owners can upload logo" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'agency-logos' AND
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM public.agencies
            WHERE user_id = auth.uid() 
            AND id::text = (storage.foldername(name))[1]
        )
    );

-- Agency owners can update their own logo
CREATE POLICY "Agency owners can update logo" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'agency-logos' AND
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM public.agencies
            WHERE user_id = auth.uid() 
            AND id::text = (storage.foldername(name))[1]
        )
    );

-- Agency owners can delete their own logo
CREATE POLICY "Agency owners can delete logo" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'agency-logos' AND
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM public.agencies
            WHERE user_id = auth.uid() 
            AND id::text = (storage.foldername(name))[1]
        )
    );

-- =============================================
-- USER AVATARS BUCKET POLICIES
-- =============================================

-- Anyone can view user avatars (public access)
CREATE POLICY "User avatars public read" ON storage.objects
    FOR SELECT USING (bucket_id = 'user-avatars');

-- Users can upload their own avatar
CREATE POLICY "Users can upload own avatar" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'user-avatars' AND
        auth.uid() IS NOT NULL AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Users can update their own avatar
CREATE POLICY "Users can update own avatar" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'user-avatars' AND
        auth.uid() IS NOT NULL AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Users can delete their own avatar
CREATE POLICY "Users can delete own avatar" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'user-avatars' AND
        auth.uid() IS NOT NULL AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- =============================================
-- DOCUMENTS BUCKET POLICIES (PRIVATE)
-- =============================================

-- Agency owners can view their own documents
CREATE POLICY "Agency owners can view own documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'documents' AND
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM public.agencies
            WHERE user_id = auth.uid() 
            AND id::text = (storage.foldername(name))[1]
        )
    );

-- Agency owners can upload their own documents
CREATE POLICY "Agency owners can upload documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'documents' AND
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM public.agencies
            WHERE user_id = auth.uid() 
            AND id::text = (storage.foldername(name))[1]
        )
    );

-- Agency owners can update their own documents
CREATE POLICY "Agency owners can update documents" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'documents' AND
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM public.agencies
            WHERE user_id = auth.uid() 
            AND id::text = (storage.foldername(name))[1]
        )
    );

-- Agency owners can delete their own documents
CREATE POLICY "Agency owners can delete documents" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'documents' AND
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM public.agencies
            WHERE user_id = auth.uid() 
            AND id::text = (storage.foldername(name))[1]
        )
    );

-- =============================================
-- ADMIN POLICIES FOR ALL BUCKETS
-- =============================================

-- Admins can manage all storage objects
CREATE POLICY "Admins can manage all storage" ON storage.objects
    FOR ALL USING (public.is_admin());

-- =============================================
-- ENSURE BUCKETS EXIST
-- =============================================

-- Create buckets if they don't exist (this will be ignored if they already exist)
INSERT INTO storage.buckets (id, name, public) 
VALUES 
    ('car-images', 'car-images', true),
    ('agency-logos', 'agency-logos', true),
    ('user-avatars', 'user-avatars', true),
    ('documents', 'documents', false)
ON CONFLICT (id) DO NOTHING;
