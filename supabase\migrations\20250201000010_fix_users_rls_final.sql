-- Final fix for users table RLS - use simplest possible approach
-- Disable <PERSON><PERSON> temporarily and create minimal policies

-- Disable R<PERSON> on users table
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS "Anyone can view user profiles" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Users can create own profile" ON public.users;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all users" ON public.users;

-- Re-enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Create minimal, non-recursive policies
CREATE POLICY "Public read access" ON public.users
    FOR SELECT USING (true);

CREATE POLICY "Users own profile update" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users own profile insert" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Simple service role policy (no recursion)
CREATE POLICY "Service role access" ON public.users
    FOR ALL USING (auth.role() = 'service_role');
