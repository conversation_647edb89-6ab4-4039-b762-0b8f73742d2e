-- Comprehensive RLS Policies for KriwDrive
-- This file re-enables R<PERSON> with properly designed policies

-- =============================================
-- ENABLE RLS ON ALL TABLES
-- =============================================

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cars ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.gps_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contact_messages ENABLE ROW LEVEL SECURITY;

-- =============================================
-- DROP EXISTING POLICIES
-- =============================================

-- Users policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Anyone can view users" ON public.users;
DROP POLICY IF EXISTS "Users can insert own data" ON public.users;

-- Agencies policies  
DROP POLICY IF EXISTS "Anyone can view agencies" ON public.agencies;
DROP POLICY IF EXISTS "Agency owners can update their agency" ON public.agencies;
DROP POLICY IF EXISTS "Authenticated users can create agencies" ON public.agencies;
DROP POLICY IF EXISTS "Agency owners can insert their agency" ON public.agencies;
DROP POLICY IF EXISTS "Authenticated users can register as agencies" ON public.agencies;

-- Cars policies
DROP POLICY IF EXISTS "Cars are viewable by all" ON public.cars;
DROP POLICY IF EXISTS "Agency owners can manage their cars" ON public.cars;
DROP POLICY IF EXISTS "Admins can manage all cars" ON public.cars;
DROP POLICY IF EXISTS "Enable read access for all cars" ON public.cars;
DROP POLICY IF EXISTS "Enable insert for authenticated agencies only" ON public.cars;
DROP POLICY IF EXISTS "Enable update for cars based on agency_id" ON public.cars;
DROP POLICY IF EXISTS "Enable delete for cars based on agency_id" ON public.cars;

-- Bookings policies
DROP POLICY IF EXISTS "Users can view their own bookings" ON public.bookings;
DROP POLICY IF EXISTS "Agency owners can view their agency bookings" ON public.bookings;
DROP POLICY IF EXISTS "Anyone can create bookings" ON public.bookings;
DROP POLICY IF EXISTS "Guest bookings can be created" ON public.bookings;

-- Reviews policies
DROP POLICY IF EXISTS "Anyone can view reviews" ON public.reviews;
DROP POLICY IF EXISTS "Users can create reviews for their bookings" ON public.reviews;

-- Other table policies
DROP POLICY IF EXISTS "Anyone can view cities" ON public.cities;
DROP POLICY IF EXISTS "Anyone can view blog posts" ON public.blog_posts;
DROP POLICY IF EXISTS "Admins can manage blog posts" ON public.blog_posts;

-- =============================================
-- USERS TABLE POLICIES
-- =============================================

-- Users can view all user profiles (for public info)
CREATE POLICY "Anyone can view user profiles" ON public.users
    FOR SELECT USING (true);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Users can insert their own profile during registration
CREATE POLICY "Users can create own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Admins can manage all users
CREATE POLICY "Admins can manage all users" ON public.users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- AGENCIES TABLE POLICIES
-- =============================================

-- Anyone can view approved agencies (for public listings)
CREATE POLICY "Anyone can view approved agencies" ON public.agencies
    FOR SELECT USING (is_approved = true OR auth.uid() = user_id);

-- Agency owners can view their own agency (even if not approved)
CREATE POLICY "Agency owners can view own agency" ON public.agencies
    FOR SELECT USING (auth.uid() = user_id);

-- Agency owners can update their own agency
CREATE POLICY "Agency owners can update own agency" ON public.agencies
    FOR UPDATE USING (auth.uid() = user_id);

-- Authenticated users can create agencies
CREATE POLICY "Authenticated users can create agencies" ON public.agencies
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND 
        auth.uid() = user_id
    );

-- Admins can manage all agencies
CREATE POLICY "Admins can manage all agencies" ON public.agencies
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- CARS TABLE POLICIES
-- =============================================

-- Anyone can view cars from approved agencies
CREATE POLICY "Anyone can view approved agency cars" ON public.cars
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.agencies
            WHERE id = agency_id AND is_approved = true
        )
    );

-- Agency owners can view their own cars
CREATE POLICY "Agency owners can view own cars" ON public.cars
    FOR SELECT USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Agency owners can insert cars for their agency
CREATE POLICY "Agency owners can insert own cars" ON public.cars
    FOR INSERT WITH CHECK (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Agency owners can update their own cars
CREATE POLICY "Agency owners can update own cars" ON public.cars
    FOR UPDATE USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Agency owners can delete their own cars
CREATE POLICY "Agency owners can delete own cars" ON public.cars
    FOR DELETE USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Admins can manage all cars
CREATE POLICY "Admins can manage all cars" ON public.cars
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- BOOKINGS TABLE POLICIES
-- =============================================

-- Users can view their own bookings
CREATE POLICY "Users can view own bookings" ON public.bookings
    FOR SELECT USING (auth.uid() = user_id);

-- Agency owners can view bookings for their cars
CREATE POLICY "Agency owners can view agency bookings" ON public.bookings
    FOR SELECT USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Authenticated users can create bookings
CREATE POLICY "Authenticated users can create bookings" ON public.bookings
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND
        (auth.uid() = user_id OR user_id IS NULL)
    );

-- Users can update their own bookings (before confirmation)
CREATE POLICY "Users can update own bookings" ON public.bookings
    FOR UPDATE USING (
        auth.uid() = user_id AND status IN ('pending', 'confirmed')
    );

-- Agency owners can update bookings for their cars
CREATE POLICY "Agency owners can update agency bookings" ON public.bookings
    FOR UPDATE USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Admins can manage all bookings
CREATE POLICY "Admins can manage all bookings" ON public.bookings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- REVIEWS TABLE POLICIES
-- =============================================

-- Anyone can view reviews
CREATE POLICY "Anyone can view reviews" ON public.reviews
    FOR SELECT USING (true);

-- Users can create reviews for their completed bookings
CREATE POLICY "Users can create reviews for own bookings" ON public.reviews
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM public.bookings
            WHERE id = booking_id
            AND user_id = auth.uid()
            AND status = 'completed'
        )
    );

-- Users can update their own reviews
CREATE POLICY "Users can update own reviews" ON public.reviews
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.bookings
            WHERE id = booking_id AND user_id = auth.uid()
        )
    );

-- Admins can manage all reviews
CREATE POLICY "Admins can manage all reviews" ON public.reviews
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- PAYMENTS TABLE POLICIES
-- =============================================

-- Users can view their own payments
CREATE POLICY "Users can view own payments" ON public.payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.bookings
            WHERE id = booking_id AND user_id = auth.uid()
        )
    );

-- Agency owners can view payments for their bookings
CREATE POLICY "Agency owners can view agency payments" ON public.payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.bookings b
            JOIN public.agencies a ON b.agency_id = a.id
            WHERE b.id = booking_id AND a.user_id = auth.uid()
        )
    );

-- System can create payments
CREATE POLICY "System can create payments" ON public.payments
    FOR INSERT WITH CHECK (true);

-- Admins can manage all payments
CREATE POLICY "Admins can manage all payments" ON public.payments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- CITIES TABLE POLICIES
-- =============================================

-- Anyone can view cities
CREATE POLICY "Anyone can view cities" ON public.cities
    FOR SELECT USING (true);

-- Admins can manage cities
CREATE POLICY "Admins can manage cities" ON public.cities
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- GPS TRACKING TABLE POLICIES
-- =============================================

-- Agency owners can view GPS data for their cars
CREATE POLICY "Agency owners can view car GPS data" ON public.gps_tracking
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.cars c
            JOIN public.agencies a ON c.agency_id = a.id
            WHERE c.id = car_id AND a.user_id = auth.uid()
        )
    );

-- System can insert GPS data
CREATE POLICY "System can insert GPS data" ON public.gps_tracking
    FOR INSERT WITH CHECK (true);

-- Admins can manage all GPS data
CREATE POLICY "Admins can manage all GPS data" ON public.gps_tracking
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- NOTIFICATIONS TABLE POLICIES
-- =============================================

-- Users can view their own notifications
CREATE POLICY "Users can view own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

-- System can create notifications
CREATE POLICY "System can create notifications" ON public.notifications
    FOR INSERT WITH CHECK (true);

-- Users can update their own notifications (mark as read)
CREATE POLICY "Users can update own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- =============================================
-- BLOG POSTS TABLE POLICIES
-- =============================================

-- Anyone can view published blog posts
CREATE POLICY "Anyone can view published blog posts" ON public.blog_posts
    FOR SELECT USING (status = 'published');

-- Admins can manage all blog posts
CREATE POLICY "Admins can manage all blog posts" ON public.blog_posts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- CONTACT MESSAGES TABLE POLICIES
-- =============================================

-- Anyone can create contact messages
CREATE POLICY "Anyone can create contact messages" ON public.contact_messages
    FOR INSERT WITH CHECK (true);

-- Admins can view and manage contact messages
CREATE POLICY "Admins can manage contact messages" ON public.contact_messages
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
