-- Re-enable RLS for core tables only
-- This migration only handles RLS policies for existing tables

-- =============================================
-- ENABLE RLS ON CORE TABLES
-- =============================================

ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cars ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;

-- =============================================
-- DROP ALL EXISTING POLICIES (SAFE)
-- =============================================

-- Users policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Anyone can view users" ON public.users;
DROP POLICY IF EXISTS "Users can insert own data" ON public.users;
DROP POLICY IF EXISTS "Anyone can view user profiles" ON public.users;
DROP POLICY IF EXISTS "Users can create own profile" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;

-- Agencies policies  
DROP POLICY IF EXISTS "Anyone can view agencies" ON public.agencies;
DROP POLICY IF EXISTS "Agency owners can update their agency" ON public.agencies;
DROP POLICY IF EXISTS "Authenticated users can create agencies" ON public.agencies;
DROP POLICY IF EXISTS "Agency owners can insert their agency" ON public.agencies;
DROP POLICY IF EXISTS "Authenticated users can register as agencies" ON public.agencies;
DROP POLICY IF EXISTS "Anyone can view approved agencies" ON public.agencies;
DROP POLICY IF EXISTS "Agency owners can view own agency" ON public.agencies;
DROP POLICY IF EXISTS "Agency owners can update own agency" ON public.agencies;
DROP POLICY IF EXISTS "Admins can manage all agencies" ON public.agencies;

-- Cars policies
DROP POLICY IF EXISTS "Cars are viewable by all" ON public.cars;
DROP POLICY IF EXISTS "Agency owners can manage their cars" ON public.cars;
DROP POLICY IF EXISTS "Admins can manage all cars" ON public.cars;
DROP POLICY IF EXISTS "Enable read access for all cars" ON public.cars;
DROP POLICY IF EXISTS "Enable insert for authenticated agencies only" ON public.cars;
DROP POLICY IF EXISTS "Enable update for cars based on agency_id" ON public.cars;
DROP POLICY IF EXISTS "Enable delete for cars based on agency_id" ON public.cars;
DROP POLICY IF EXISTS "Anyone can view approved agency cars" ON public.cars;
DROP POLICY IF EXISTS "Agency owners can view own cars" ON public.cars;
DROP POLICY IF EXISTS "Agency owners can insert own cars" ON public.cars;
DROP POLICY IF EXISTS "Agency owners can update own cars" ON public.cars;
DROP POLICY IF EXISTS "Agency owners can delete own cars" ON public.cars;

-- Bookings policies
DROP POLICY IF EXISTS "Users can view their own bookings" ON public.bookings;
DROP POLICY IF EXISTS "Agency owners can view their agency bookings" ON public.bookings;
DROP POLICY IF EXISTS "Anyone can create bookings" ON public.bookings;
DROP POLICY IF EXISTS "Guest bookings can be created" ON public.bookings;
DROP POLICY IF EXISTS "Users can view own bookings" ON public.bookings;
DROP POLICY IF EXISTS "Agency owners can view agency bookings" ON public.bookings;
DROP POLICY IF EXISTS "Authenticated users can create bookings" ON public.bookings;
DROP POLICY IF EXISTS "Users can update own bookings" ON public.bookings;
DROP POLICY IF EXISTS "Agency owners can update agency bookings" ON public.bookings;
DROP POLICY IF EXISTS "Admins can manage all bookings" ON public.bookings;

-- =============================================
-- USERS TABLE POLICIES
-- =============================================

-- Anyone can view user profiles (for public info like names, avatars)
CREATE POLICY "Anyone can view user profiles" ON public.users
    FOR SELECT USING (true);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Users can insert their own profile during registration
CREATE POLICY "Users can create own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Admins can manage all users
CREATE POLICY "Admins can manage all users" ON public.users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- AGENCIES TABLE POLICIES
-- =============================================

-- Anyone can view approved agencies (for public listings)
CREATE POLICY "Anyone can view approved agencies" ON public.agencies
    FOR SELECT USING (is_approved = true);

-- Agency owners can view their own agency (even if not approved)
CREATE POLICY "Agency owners can view own agency" ON public.agencies
    FOR SELECT USING (auth.uid() = user_id);

-- Agency owners can update their own agency
CREATE POLICY "Agency owners can update own agency" ON public.agencies
    FOR UPDATE USING (auth.uid() = user_id);

-- Authenticated users can create agencies
CREATE POLICY "Authenticated users can create agencies" ON public.agencies
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND 
        auth.uid() = user_id
    );

-- Admins can manage all agencies
CREATE POLICY "Admins can manage all agencies" ON public.agencies
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- CARS TABLE POLICIES
-- =============================================

-- Anyone can view cars from approved agencies
CREATE POLICY "Anyone can view approved agency cars" ON public.cars
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.agencies
            WHERE id = agency_id AND is_approved = true
        )
    );

-- Agency owners can view their own cars
CREATE POLICY "Agency owners can view own cars" ON public.cars
    FOR SELECT USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Agency owners can insert cars for their agency
CREATE POLICY "Agency owners can insert own cars" ON public.cars
    FOR INSERT WITH CHECK (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Agency owners can update their own cars
CREATE POLICY "Agency owners can update own cars" ON public.cars
    FOR UPDATE USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Agency owners can delete their own cars
CREATE POLICY "Agency owners can delete own cars" ON public.cars
    FOR DELETE USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Admins can manage all cars
CREATE POLICY "Admins can manage all cars" ON public.cars
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- BOOKINGS TABLE POLICIES
-- =============================================

-- Users can view their own bookings
CREATE POLICY "Users can view own bookings" ON public.bookings
    FOR SELECT USING (auth.uid() = user_id);

-- Agency owners can view bookings for their cars
CREATE POLICY "Agency owners can view agency bookings" ON public.bookings
    FOR SELECT USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Authenticated users can create bookings
CREATE POLICY "Authenticated users can create bookings" ON public.bookings
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR user_id IS NULL)
    );

-- Agency owners can update bookings for their cars
CREATE POLICY "Agency owners can update agency bookings" ON public.bookings
    FOR UPDATE USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

-- Admins can manage all bookings
CREATE POLICY "Admins can manage all bookings" ON public.bookings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
