-- Implement proper admin role and fix users table RLS
-- This migration creates a proper admin system without recursion

-- =============================================
-- CREATE ADMIN ROLE FUNCTION
-- =============================================

-- Create a function to check if user is admin using JWT claims
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if the user has admin role in their JWT claims or is service role
  RETURN (auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin') OR
         (auth.jwt() ->> 'app_metadata' ->> 'role' = 'admin') OR
         (auth.role() = 'service_role');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if user is admin from database (non-recursive)
CREATE OR REPLACE FUNCTION public.is_user_admin(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- Direct query without RLS to avoid recursion
  SELECT role INTO user_role FROM public.users WHERE id = user_id;
  RETURN user_role = 'admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- RE-ENABLE USERS TABLE RLS WITH PROPER POLICIES
-- =============================================

-- Enable RLS on users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies
DROP POLICY IF EXISTS "Public user info viewable" ON public.users;
DROP POLICY IF EXISTS "Users update own profile" ON public.users;
DROP POLICY IF EXISTS "Users create own profile" ON public.users;
DROP POLICY IF EXISTS "Service role full access" ON public.users;

-- Create new non-recursive policies
CREATE POLICY "Anyone can view user profiles" ON public.users
    FOR SELECT USING (true);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can create own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Admin policy using JWT claims instead of table lookup
CREATE POLICY "Admins can manage all users" ON public.users
    FOR ALL USING (public.is_admin());

-- =============================================
-- UPDATE OTHER TABLE POLICIES TO USE NEW ADMIN FUNCTION
-- =============================================

-- Update agencies policies
DROP POLICY IF EXISTS "Service role can manage all agencies" ON public.agencies;
CREATE POLICY "Admins can manage all agencies" ON public.agencies
    FOR ALL USING (public.is_admin());

-- Update cars policies
DROP POLICY IF EXISTS "Service role can manage all cars" ON public.cars;
CREATE POLICY "Admins can manage all cars" ON public.cars
    FOR ALL USING (public.is_admin());

-- Update bookings policies
DROP POLICY IF EXISTS "Service role can manage all bookings" ON public.bookings;
CREATE POLICY "Admins can manage all bookings" ON public.bookings
    FOR ALL USING (public.is_admin());

-- Update cities policies
DROP POLICY IF EXISTS "Service role can manage cities" ON public.cities;
CREATE POLICY "Admins can manage cities" ON public.cities
    FOR ALL USING (public.is_admin());

-- =============================================
-- CREATE ADMIN USER FUNCTION
-- =============================================

-- Function to promote a user to admin (can only be called by service role)
CREATE OR REPLACE FUNCTION public.promote_user_to_admin(user_email TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  target_user_id UUID;
BEGIN
  -- Only service role can call this function
  IF auth.role() != 'service_role' THEN
    RAISE EXCEPTION 'Only service role can promote users to admin';
  END IF;

  -- Find user by email
  SELECT id INTO target_user_id FROM public.users WHERE email = user_email;
  
  IF target_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found with email: %', user_email;
  END IF;

  -- Update user role to admin
  UPDATE public.users SET role = 'admin' WHERE id = target_user_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- GRANT NECESSARY PERMISSIONS
-- =============================================

-- Grant execute permission on admin functions
GRANT EXECUTE ON FUNCTION public.is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_user_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.promote_user_to_admin(TEXT) TO service_role;
